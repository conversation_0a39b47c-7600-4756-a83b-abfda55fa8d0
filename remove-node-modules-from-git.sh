#!/bin/bash

echo "Removing node_modules from Git tracking..."
echo

echo "Step 1: Removing cached node_modules from git index..."
git rm -r --cached "PanlasApp Website/panlasapp web/node_modules" 2>/dev/null || true
git rm -r --cached "PanlasApp Website/panlasapp web/meal-planner-backend/node_modules" 2>/dev/null || true
git rm -r --cached "panlas-mobile-app/node_modules" 2>/dev/null || true
git rm -r --cached node_modules 2>/dev/null || true
git rm -r --cached */node_modules 2>/dev/null || true
git rm -r --cached **/node_modules 2>/dev/null || true

echo
echo "Step 2: Adding .gitignore files to git..."
git add .gitignore
git add "PanlasApp Website/panlasapp web/.gitignore"
git add "PanlasApp Website/panlasapp web/meal-planner-backend/.gitignore"
git add "panlas-mobile-app/.gitignore"

echo
echo "Step 3: Committing changes..."
git commit -m "Add .gitignore files and remove node_modules from tracking"

echo
echo "Done! node_modules should now be ignored by git."
echo
echo "Next steps:"
echo "1. Run 'git status' to verify node_modules are no longer tracked"
echo "2. If you see any remaining node_modules files, run:"
echo "   git rm -r --cached [path-to-node_modules]"
echo "3. Commit any remaining changes"
echo
