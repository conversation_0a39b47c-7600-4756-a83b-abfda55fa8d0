import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { colors, legacyFonts, legacySpacing, borderRadius } from '../../styles/theme';

const WelcomeScreen = ({ navigation }) => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {/* Logo/Hero Section */}
        <View style={styles.heroSection}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>🍽️</Text>
          </View>
          <Text style={styles.title}>Panlas</Text>
          <Text style={styles.subtitle}>Filipino Meal Planner</Text>
          <Text style={styles.tagline}>
            Discover authentic Filipino recipes and plan your meals with ease
          </Text>
        </View>

        {/* Features Section */}
        <View style={styles.featuresSection}>
          <View style={styles.feature}>
            <Text style={styles.featureIcon}>🥘</Text>
            <Text style={styles.featureText}>Authentic Filipino Recipes</Text>
          </View>
          <View style={styles.feature}>
            <Text style={styles.featureIcon}>📅</Text>
            <Text style={styles.featureText}>Smart Meal Planning</Text>
          </View>
          <View style={styles.feature}>
            <Text style={styles.featureIcon}>❤️</Text>
            <Text style={styles.featureText}>Save Your Favorites</Text>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={() => navigation.navigate('Login')}
          >
            <Text style={styles.primaryButtonText}>Get Started</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: legacySpacing.lg,
    justifyContent: 'space-between',
    paddingTop: legacySpacing.xxl,
    paddingBottom: legacySpacing.xl,
  },
  heroSection: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  logoContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: legacySpacing.lg,
  },
  logoText: {
    fontSize: 60,
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    color: colors.surface,
    marginBottom: legacySpacing.sm,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: legacyFonts.sizes.large,
    color: colors.surface,
    marginBottom: legacySpacing.md,
    textAlign: 'center',
    opacity: 0.9,
  },
  tagline: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.surface,
    textAlign: 'center',
    opacity: 0.8,
    lineHeight: 24,
    paddingHorizontal: legacySpacing.md,
  },
  featuresSection: {
    marginVertical: legacySpacing.xl,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: legacySpacing.md,
    paddingHorizontal: legacySpacing.md,
  },
  featureIcon: {
    fontSize: 24,
    marginRight: legacySpacing.md,
  },
  featureText: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.surface,
    fontWeight: '500',
  },
  buttonContainer: {
    gap: legacySpacing.md,
  },
  primaryButton: {
    backgroundColor: colors.secondary,
    paddingVertical: legacySpacing.md,
    borderRadius: borderRadius.large,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  primaryButtonText: {
    color: colors.surface,
    fontSize: legacyFonts.sizes.medium,
    fontWeight: 'bold',
  },
});

export default WelcomeScreen;
