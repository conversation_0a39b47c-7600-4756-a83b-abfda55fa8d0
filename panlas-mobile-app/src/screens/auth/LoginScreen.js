import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../context/AuthContext';
import TermsModal from '../../components/TermsModal';
import termsService from '../../services/termsService';
import { colors, legacyFonts, legacySpacing, borderRadius } from '../../styles/theme';

const LoginScreen = ({ navigation, route }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [loading, setLoading] = useState(false);
  const [fieldErrors, setFieldErrors] = useState({});
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [loginResponse, setLoginResponse] = useState(null);

  const { login, completeLoginAfterTerms } = useAuth();

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    validateFieldRealTime(field, value);
  };

  const validateFieldRealTime = (fieldName, value) => {
    const newErrors = { ...fieldErrors };

    switch (fieldName) {
      case 'email':
        if (!value) {
          newErrors.email = 'Email is required';
        } else if (!/^[^\s@]+@gmail\.com$/i.test(value)) {
          newErrors.email = 'Email must be gmail';
        } else {
          delete newErrors.email;
        }
        break;

      case 'password':
        if (!value) {
          newErrors.password = 'Password is required';
        } else if (value.length < 5) {
          newErrors.password = 'Password must be at least 5 characters';
        } else {
          delete newErrors.password;
        }
        break;

      default:
        break;
    }

    setFieldErrors(newErrors);
  };

  // Pre-fill email if coming from registration
  useEffect(() => {
    if (route.params?.prefillEmail) {
      setFormData(prev => ({
        ...prev,
        email: route.params.prefillEmail
      }));
    }
  }, [route.params]);

  const handleLogin = async () => {
    if (!formData.email || !formData.password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    setLoading(true);
    try {
      const result = await login({
        email: formData.email.trim(),
        password: formData.password,
      });

      if (result.success) {
        if (result.requiresTermsAcceptance) {
          // Show terms modal for first-time login
          setLoginResponse(result.data);
          setShowTermsModal(true);
        }
        // If no terms required, login is already completed by AuthContext
      } else {
        Alert.alert('Login Failed', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleTermsAccept = async () => {
    try {
      const result = await termsService.acceptTerms();

      if (result.success) {
        setShowTermsModal(false);
        // Complete the login process
        const completeResult = await completeLoginAfterTerms(loginResponse);
        if (!completeResult.success) {
          Alert.alert('Error', 'Failed to complete login after accepting terms');
        }
      } else {
        Alert.alert('Error', 'Failed to accept terms: ' + result.error);
      }
    } catch (error) {
      console.error('Error accepting terms:', error);
      Alert.alert('Error', 'Failed to accept terms. Please try again.');
    }
  };

  const handleTermsDecline = () => {
    Alert.alert(
      'Terms Required',
      'You must accept the terms and conditions to use PanlasApp.',
      [
        {
          text: 'OK',
          onPress: async () => {
            setShowTermsModal(false);
            setLoginResponse(null);
            // Clear any stored token since login is not complete
            await AsyncStorage.removeItem('token');
          }
        }
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Sign In</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Content */}
      <View style={styles.content}>
        <View style={styles.welcomeSection}>
          <Text style={styles.title}>Welcome Back!</Text>
          <Text style={styles.subtitle}>
            {route.params?.prefillEmail
              ? 'We found an existing account with this email'
              : 'Sign in to continue to Panlas'
            }
          </Text>
        </View>

        <View style={styles.formSection}>
          {/* Email Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Email</Text>
            <TextInput
              style={[styles.input, fieldErrors.email && styles.inputError]}
              placeholder="Enter your email"
              value={formData.email}
              onChangeText={(text) => updateFormData('email', text)}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
            {fieldErrors.email && (
              <Text style={styles.errorText}>{fieldErrors.email}</Text>
            )}
          </View>

          {/* Password Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Password</Text>
            <TextInput
              style={[styles.input, fieldErrors.password && styles.inputError]}
              placeholder="Enter your password"
              value={formData.password}
              onChangeText={(text) => updateFormData('password', text)}
              secureTextEntry
              autoCapitalize="none"
            />
            {fieldErrors.password && (
              <Text style={styles.errorText}>{fieldErrors.password}</Text>
            )}
          </View>

          {/* Forgot Password Link */}
          <TouchableOpacity
            style={styles.forgotPasswordContainer}
            onPress={() => navigation.navigate('ForgotPassword')}
          >
            <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
          </TouchableOpacity>

          {/* Login Button */}
          <TouchableOpacity
            style={[styles.loginButton, loading && styles.disabledButton]}
            onPress={handleLogin}
            disabled={loading}
          >
            <Text style={styles.loginButtonText}>
              {loading ? 'Signing In...' : 'Sign In'}
            </Text>
          </TouchableOpacity>

          {/* Forgot Password Link */}
          <TouchableOpacity
            style={styles.forgotPasswordButton}
            onPress={() => navigation.navigate('ForgotPassword')}
          >
            <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
          </TouchableOpacity>

          {/* Register Link */}
          <View style={styles.registerSection}>
            <Text style={styles.registerText}>Don't have an account? </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Register')}>
              <Text style={styles.registerLink}>Create Account</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Terms and Conditions Modal */}
      <TermsModal
        visible={showTermsModal}
        onAccept={handleTermsAccept}
        onDecline={handleTermsDecline}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: legacySpacing.sm,
  },
  headerTitle: {
    fontSize: legacyFonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: legacySpacing.lg,
  },
  welcomeSection: {
    alignItems: 'center',
    marginBottom: legacySpacing.xl,
    marginTop: legacySpacing.lg,
  },
  title: {
    fontSize: legacyFonts.sizes.xxlarge,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: legacySpacing.sm,
  },
  subtitle: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  formSection: {
    flex: 1,
  },
  inputContainer: {
    marginBottom: legacySpacing.lg,
  },
  inputLabel: {
    fontSize: legacyFonts.sizes.medium,
    fontWeight: '500',
    color: colors.text,
    marginBottom: legacySpacing.sm,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.md,
    fontSize: legacyFonts.sizes.medium,
    backgroundColor: colors.surface,
  },
  loginButton: {
    backgroundColor: colors.primary,
    paddingVertical: legacySpacing.md,
    borderRadius: borderRadius.medium,
    alignItems: 'center',
    marginTop: legacySpacing.lg,
  },
  loginButtonText: {
    color: colors.surface,
    fontSize: legacyFonts.sizes.medium,
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.6,
  },
  forgotPasswordButton: {
    alignItems: 'center',
    marginTop: legacySpacing.md,
    marginBottom: legacySpacing.sm,
  },
  forgotPasswordText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  registerSection: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: legacySpacing.lg,
  },
  registerText: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.textSecondary,
  },
  registerLink: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.primary,
    fontWeight: 'bold',
  },
  forgotPasswordContainer: {
    alignItems: 'flex-end',
    marginBottom: legacySpacing.sm,
  },
  forgotPasswordText: {
    fontSize: legacyFonts.sizes.small,
    color: colors.primary,
    fontWeight: '500',
  },
  inputError: {
    borderColor: '#FF5252',
    borderWidth: 1,
  },
  errorText: {
    fontSize: 12,
    color: '#FF5252',
    marginTop: 4,
    marginLeft: 4,
  },
});

export default LoginScreen;
