import React from 'react';
import { View, Text, StyleSheet, SafeAreaView } from 'react-native';
import { colors, fonts } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

const SearchScreen = () => {
  return (
    <SafeAreaView style={commonStyles.container}>
      <View style={commonStyles.centerContent}>
        <Text style={styles.placeholder}>Search feature coming soon...</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  placeholder: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});

export default SearchScreen;
