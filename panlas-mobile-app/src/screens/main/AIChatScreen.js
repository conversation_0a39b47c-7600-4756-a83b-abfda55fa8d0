import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  TextInput,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useAuth } from '../../context/AuthContext';
import { aiAPI, mealPlansAPI, mealsAPI } from '../../services/api';
import { colors, legacyFonts as fonts, legacySpacing as spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

const AIChatScreen = ({ navigation }) => {
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedGoal, setSelectedGoal] = useState(null);
  const [selectedHealthCondition, setSelectedHealthCondition] = useState(null);
  const [showGoalSelection, setShowGoalSelection] = useState(true);
  const [showChatInput, setShowChatInput] = useState(false);
  const [goals, setGoals] = useState([]);
  const [healthConditions, setHealthConditions] = useState([]);
  const [generatedMealPlan, setGeneratedMealPlan] = useState(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [editingMealType, setEditingMealType] = useState(null);
  const [showMealTypeSelection, setShowMealTypeSelection] = useState(false);
  const [isEditingMode, setIsEditingMode] = useState(false);
  
  const scrollViewRef = useRef();
  const { user } = useAuth();

  const defaultGoals = [
    { id: 'lose_weight', name: 'Lose Weight', description: 'Get meal suggestions to help with weight loss' },
    { id: 'build_muscle', name: 'Build Muscle', description: 'Get high-protein meals to support muscle building' },
    { id: 'manage_health', name: 'Manage a Health Condition', description: 'Get meals tailored to specific health conditions' },
    { id: 'eat_sustainably', name: 'Eat Sustainably', description: 'Get environmentally conscious meal suggestions' },
    { id: 'generate_meal_plan', name: 'Generate a meal plan', description: 'Create a personalized daily meal plan based on your dietary preferences' },
    { id: 'other', name: 'Other', description: 'Chat directly with AI for custom dietary advice' }
  ];

  const defaultHealthConditions = [
    { id: 'type2_diabetes', name: 'Type 2 Diabetes', description: 'Low-sugar, low-carb meal recommendations' },
    { id: 'celiac_disease', name: 'Celiac Disease', description: 'Gluten-free meal recommendations' },
    { id: 'hypertension', name: 'Hypertension', description: 'Low-sodium meal recommendations' },
    { id: 'heart_disease', name: 'Heart Disease', description: 'Heart-healthy, low-cholesterol meals' },
    { id: 'lactose_intolerance', name: 'Lactose Intolerance', description: 'Dairy-free meal recommendations' }
  ];

  useEffect(() => {
    loadGoalsAndConditions();
    addWelcomeMessage();
  }, []);

  const loadGoalsAndConditions = async () => {
    try {
      const response = await aiAPI.getGoals();
      if (response.data.success) {
        setGoals(response.data.goals || defaultGoals);
        setHealthConditions(response.data.healthConditions || defaultHealthConditions);
      } else {
        setGoals(defaultGoals);
        setHealthConditions(defaultHealthConditions);
      }
    } catch (error) {
      console.error('Error loading goals:', error);
      setGoals(defaultGoals);
      setHealthConditions(defaultHealthConditions);
    }
  };

  const addWelcomeMessage = () => {
    const userName = user?.firstName || 'User';
    const welcomeMessage = {
      id: Date.now(),
      text: `Hello ${userName}! I'm your AI meal planning assistant. To get started, please select one of your health goals below, and I'll provide personalized dietary recommendations for you and your family.`,
      isUser: false,
      timestamp: new Date(),
    };
    setMessages([welcomeMessage]);
  };

  const selectGoal = async (goal) => {
    setSelectedGoal(goal);
    setShowGoalSelection(false);

    const goalMessage = {
      id: Date.now(),
      text: goal.id === 'other' ? 'I want to chat about something else' :
            goal.id === 'generate_meal_plan' ? 'I want to generate a meal plan' :
            `I want to ${goal.name.toLowerCase()}`,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, goalMessage]);

    if (goal.id === 'other') {
      // For "Other", enable free chat mode permanently
      const aiMessage = {
        id: Date.now() + 1,
        text: "I'm here to help with any dietary questions or meal planning needs you have! Please tell me what you'd like to know about nutrition, meals, or healthy eating.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiMessage]);
      setShowChatInput(true);
    } else if (goal.id === 'manage_health') {
      // Show health condition selection
      const responseMessage = {
        id: Date.now() + 1,
        text: "Great! Please select the specific health condition you'd like to manage:",
        isUser: false,
        timestamp: new Date(),
        showHealthConditions: true,
      };

      setMessages(prev => [...prev, responseMessage]);
    } else if (goal.id === 'generate_meal_plan') {
      // Generate AI meal plan
      await generateAIMealPlan();
    } else {
      // Get AI suggestions for other goals
      await getGoalSuggestions(goal);
    }
  };

  const selectHealthCondition = async (condition) => {
    setSelectedHealthCondition(condition);

    const conditionMessage = {
      id: Date.now(),
      text: `I want to manage ${condition.name}`,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, conditionMessage]);

    // Get AI suggestions for the selected health condition
    await getGoalSuggestions(selectedGoal, condition);
  };

  const getGoalSuggestions = async (goal, healthCondition = null) => {
    try {
      setLoading(true);

      const response = await aiAPI.getGoalSuggestions({
        goal: goal.name,
        healthCondition: healthCondition?.name
      });

      if (response.data.success && response.data.suggestions) {
        const suggestions = response.data.suggestions;
        let responseText = suggestions.explanation;

        if (suggestions.recommendedRestrictions.length > 0) {
          responseText += `\n\n🥗 Recommended Dietary Restrictions:\n${suggestions.recommendedRestrictions.map(r => `• ${r}`).join('\n')}`;
        }

        if (suggestions.recommendedAllergies.length > 0) {
          responseText += `\n\n⚠️ Consider avoiding:\n${suggestions.recommendedAllergies.map(a => `• ${a}`).join('\n')}`;
        }

        if (suggestions.additionalTips.length > 0) {
          responseText += `\n\n💡 Additional Tips:\n${suggestions.additionalTips.map(tip => `• ${tip}`).join('\n')}`;
        }

        responseText += `\n\nWould you like me to recommend specific meals from our database that match these preferences?`;

        const aiMessage = {
          id: Date.now(),
          text: responseText,
          isUser: false,
          timestamp: new Date(),
        };

        setMessages(prev => [...prev, aiMessage]);

        // Enable chat input after AI responds so user can continue conversation
        setShowChatInput(true);
      }
    } catch (error) {
      console.error('Error getting goal suggestions:', error);
      const errorMessage = {
        id: Date.now(),
        text: "I'm sorry, I'm having trouble providing suggestions right now. Please try again later.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);

      // Enable chat input even on error so user can try again
      setShowChatInput(true);
    } finally {
      setLoading(false);
    }
  };

  const generateAIMealPlan = async () => {
    try {
      setLoading(true);

      const loadingMessage = {
        id: Date.now(),
        text: "Perfect! I'm generating a personalized meal plan based on your dietary preferences. This may take a moment...",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, loadingMessage]);

      const response = await aiAPI.generateMealPlan();
      console.log('🔍 AI Meal Plan Response:', JSON.stringify(response, null, 2));

      if (response.data && response.data.success) {
        setGeneratedMealPlan(response.data);

        // Format the meal plan for display
        const mealPlanText = formatMealPlanForDisplay(response.data);
        console.log('🍽️ Formatted Meal Plan Text:', mealPlanText);

        const aiMessage = {
          id: Date.now() + 1,
          text: `${response.data.personalizedMessage}\n\n${mealPlanText}\n\n${response.data.nutritionalSummary}\n\nWhat would you like to do with this meal plan?`,
          isUser: false,
          timestamp: new Date(),
          showMealPlanActions: true,
        };
        setMessages(prev => [...prev, aiMessage]);
        setShowChatInput(true);
      }
    } catch (error) {
      console.error('Error generating meal plan:', error);
      const errorMessage = {
        id: Date.now() + 1,
        text: "I'm sorry, I had trouble generating your meal plan. Please try again later.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };



  const formatMealPlanForDisplay = (mealPlanData) => {
    console.log('🔍 formatMealPlanForDisplay received:', JSON.stringify(mealPlanData, null, 2));
    const { mealPlan } = mealPlanData;
    let text = "🍽️ **Your Personalized Meal Plan:**\n\n";

    if (mealPlan && mealPlan.breakfast && mealPlan.breakfast.length > 0) {
      text += "🌅 **Breakfast:**\n";
      mealPlan.breakfast.forEach((meal, index) => {
        console.log(`🍳 Breakfast meal ${index + 1}:`, meal);
        text += `• ${meal.mealName || 'Unknown meal'}\n  ${meal.reason || 'No reason provided'}\n\n`;
      });
    }

    if (mealPlan && mealPlan.lunch && mealPlan.lunch.length > 0) {
      text += "☀️ **Lunch:**\n";
      mealPlan.lunch.forEach((meal, index) => {
        console.log(`🍽️ Lunch meal ${index + 1}:`, meal);
        text += `• ${meal.mealName || 'Unknown meal'}\n  ${meal.reason || 'No reason provided'}\n\n`;
      });
    }

    if (mealPlan && mealPlan.dinner && mealPlan.dinner.length > 0) {
      text += "🌙 **Dinner:**\n";
      mealPlan.dinner.forEach((meal, index) => {
        console.log(`🌙 Dinner meal ${index + 1}:`, meal);
        text += `• ${meal.mealName || 'Unknown meal'}\n  ${meal.reason || 'No reason provided'}\n\n`;
      });
    }

    if (mealPlan && mealPlan.snacks && mealPlan.snacks.length > 0) {
      text += "🍿 **Snacks:**\n";
      mealPlan.snacks.forEach((meal, index) => {
        console.log(`🍿 Snack ${index + 1}:`, meal);
        text += `• ${meal.mealName || 'Unknown meal'}\n  ${meal.reason || 'No reason provided'}\n\n`;
      });
    }

    return text;
  };

  const handleSaveMealPlan = async (date) => {
    try {
      setLoading(true);

      if (!generatedMealPlan) {
        throw new Error('No meal plan to save');
      }

      // Check if we're in editing mode
      if (editingMealType) {
        // Show meal options for editing
        const aiMessage = {
          id: Date.now(),
          text: `Perfect! Now I'll show you alternative ${editingMealType} options that match your family's dietary preferences. Please tell me which specific ${editingMealType} you'd like to replace, and I'll suggest alternatives.`,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, aiMessage]);
        setShowDatePicker(false);
        setShowChatInput(true);
        return;
      }

      // Convert AI meal plan to the format expected by saveMealPlan API
      const mealPlanData = await convertAIMealPlanToSaveFormat(generatedMealPlan, date);

      const response = await mealPlansAPI.saveMealPlan(mealPlanData);

      if (response.data && response.data.success) {
        const successMessage = {
          id: Date.now(),
          text: `Meal plan saved successfully for ${new Date(date).toLocaleDateString()}`,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, successMessage]);

        // Reset meal plan state
        setGeneratedMealPlan(null);
        setShowDatePicker(false);
        setEditingMealType(null);
      }
    } catch (error) {
      console.error('Error saving meal plan:', error);
      const errorMessage = {
        id: Date.now(),
        text: "I'm sorry, I had trouble saving your meal plan. Please try again.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const convertAIMealPlanToSaveFormat = async (aiMealPlan, date) => {
    const meals = [];
    const dateStr = new Date(date).toISOString().split('T')[0];

    // Helper function to find meal data from database
    const findMealData = async (mealName) => {
      try {
        const response = await mealsAPI.getAllMeals();
        const allMeals = response.data;
        const foundMeal = allMeals.find(meal =>
          meal.name.toLowerCase() === mealName.toLowerCase()
        );
        return foundMeal || null;
      } catch (error) {
        console.error('Error fetching meal data:', error);
        return null;
      }
    };

    // Convert breakfast meals
    if (aiMealPlan.mealPlan.breakfast) {
      for (const meal of aiMealPlan.mealPlan.breakfast) {
        const mealData = await findMealData(meal.mealName);
        meals.push({
          date: dateStr,
          mealType: 'breakfast',
          mealData: {
            name: meal.mealName,
            instanceId: `${meal.mealName}-${Date.now()}-breakfast`,
            calories: mealData?.calories || 0,
            protein: mealData?.protein || 0,
            carbs: mealData?.carbs || 0,
            fat: mealData?.fat || 0,
            category: mealData?.category || 'AI Generated',
            dietaryTags: mealData?.dietaryTags || [],
            image: mealData?.image || '',
            ingredients: mealData?.ingredients || [],
            instructions: mealData?.instructions || [],
            description: meal.reason
          }
        });
      }
    }

    // Convert lunch meals
    if (aiMealPlan.mealPlan.lunch) {
      for (const meal of aiMealPlan.mealPlan.lunch) {
        const mealData = await findMealData(meal.mealName);
        meals.push({
          date: dateStr,
          mealType: 'lunch',
          mealData: {
            name: meal.mealName,
            instanceId: `${meal.mealName}-${Date.now()}-lunch`,
            calories: mealData?.calories || 0,
            protein: mealData?.protein || 0,
            carbs: mealData?.carbs || 0,
            fat: mealData?.fat || 0,
            category: mealData?.category || 'AI Generated',
            dietaryTags: mealData?.dietaryTags || [],
            image: mealData?.image || '',
            ingredients: mealData?.ingredients || [],
            instructions: mealData?.instructions || [],
            description: meal.reason
          }
        });
      }
    }

    // Convert dinner meals
    if (aiMealPlan.mealPlan.dinner) {
      for (const meal of aiMealPlan.mealPlan.dinner) {
        const mealData = await findMealData(meal.mealName);
        meals.push({
          date: dateStr,
          mealType: 'dinner',
          mealData: {
            name: meal.mealName,
            instanceId: `${meal.mealName}-${Date.now()}-dinner`,
            calories: mealData?.calories || 0,
            protein: mealData?.protein || 0,
            carbs: mealData?.carbs || 0,
            fat: mealData?.fat || 0,
            category: mealData?.category || 'AI Generated',
            dietaryTags: mealData?.dietaryTags || [],
            image: mealData?.image || '',
            ingredients: mealData?.ingredients || [],
            instructions: mealData?.instructions || [],
            description: meal.reason
          }
        });
      }
    }

    // Convert snack meals
    if (aiMealPlan.mealPlan.snacks) {
      for (const meal of aiMealPlan.mealPlan.snacks) {
        const mealData = await findMealData(meal.mealName);
        meals.push({
          date: dateStr,
          mealType: 'snack',
          mealData: {
            name: meal.mealName,
            instanceId: `${meal.mealName}-${Date.now()}-snack`,
            calories: mealData?.calories || 0,
            protein: mealData?.protein || 0,
            carbs: mealData?.carbs || 0,
            fat: mealData?.fat || 0,
            category: mealData?.category || 'AI Generated',
            dietaryTags: mealData?.dietaryTags || [],
            image: mealData?.image || '',
            ingredients: mealData?.ingredients || [],
            instructions: mealData?.instructions || [],
            description: meal.reason
          }
        });
      }
    }

    return {
      name: `AI Generated Meal Plan - ${new Date(date).toLocaleDateString()}`,
      startDate: dateStr,
      endDate: dateStr,
      dietaryPreference: 'all',
      meals: meals,
      mealTimes: {
        breakfast: '08:00',
        lunch: '12:00',
        dinner: '18:00',
        snacks: '15:00'
      }
    };
  };

  const sendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage = {
      id: Date.now(),
      text: inputText,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    const messageText = inputText;
    setInputText('');
    setLoading(true);

    // Define direct edit patterns at the top level so they're accessible throughout
    const directEditPatterns = [
      /replace\s+(.+?)\s+with\s+(.+)/i,
      /change\s+(.+?)\s+to\s+(.+)/i,
      /swap\s+(.+?)\s+with\s+(.+)/i,
      /substitute\s+(.+?)\s+with\s+(.+)/i,
      /i want to replace\s+(.+?)\s+with\s+(.+)/i,
      /i want to change\s+(.+?)\s+to\s+(.+)/i,
      /i want to swap\s+(.+?)\s+with\s+(.+)/i
    ];

    const isDirectEditRequest = directEditPatterns.some(pattern => pattern.test(messageText.toLowerCase()));

    try {
      // Check if user is responding about meal plan actions
      if (generatedMealPlan) {
        // Check for Save option
        if (messageText.toLowerCase().includes('save') || messageText.toLowerCase().includes('calendar')) {
          const aiMessage = {
            id: Date.now() + 1,
            text: "Perfect! Please select the date you'd like to schedule this meal plan:",
            isUser: false,
            timestamp: new Date(),
            showDatePicker: true,
          };
          setMessages(prev => [...prev, aiMessage]);
          setShowDatePicker(true);
          return;
        }

        // If it's a direct edit request, process it immediately
        if (isDirectEditRequest && generatedMealPlan) {
          // This will be handled by the edit processing logic below
          // Don't return here, let it fall through to the edit processing
        } else {
          // Check for general edit keywords only if it's not a direct edit request
          const editKeywords = ['edit', 'update'];
          const hasGeneralEditKeyword = editKeywords.some(keyword => messageText.toLowerCase().includes(keyword));

          if (hasGeneralEditKeyword) {
            setIsEditingMode(true);
            const aiMessage = {
              id: Date.now() + 1,
              text: "Great! I can help you edit your meal plan. Please tell me which dishes you'd like to change. For example, you can say 'Replace Kare-Kare with Tapa in lunch' or 'Change breakfast to something vegetarian'.",
              isUser: false,
              timestamp: new Date(),
            };
            setMessages(prev => [...prev, aiMessage]);
            setShowChatInput(true);
            return;
          }
        }

        // Check for No thanks option (but not if it's a direct edit request)
        if (!isDirectEditRequest && (messageText.toLowerCase().includes('no') || messageText.toLowerCase().includes('not') || messageText.toLowerCase().includes('thanks'))) {
          const aiMessage = {
            id: Date.now() + 1,
            text: "No problem! Is there anything else I can help you with regarding your meal planning?",
            isUser: false,
            timestamp: new Date(),
          };
          setMessages(prev => [...prev, aiMessage]);
          setGeneratedMealPlan(null);
          setIsEditingMode(false);
          return;
        }
      }

      // Check if user is in editing mode or making a direct edit request
      if ((isEditingMode || isDirectEditRequest) && generatedMealPlan) {
        try {
          // If this is a direct edit request, automatically enter edit mode
          if (isDirectEditRequest && !isEditingMode) {
            setIsEditingMode(true);
          }

          // Send the editing request to the dedicated edit endpoint
          // Check if this is a family plan by looking at the messages for isFamilyPlan flag
          const isFamilyPlan = messages.some(msg => msg.showMealPlanActions && msg.isFamilyPlan);

          const editResponse = await aiAPI.editMealPlan({
            currentMealPlan: generatedMealPlan.mealPlan,
            editRequest: messageText,
            isFamily: isFamilyPlan
          });
          console.log('🔍 Edit Meal Plan Response:', JSON.stringify(editResponse, null, 2));

          if (editResponse.data && editResponse.data.success) {
            // Update the generated meal plan with the edited version
            const updatedMealPlan = {
              mealPlan: editResponse.data.mealPlan,
              nutritionalSummary: editResponse.data.nutritionalSummary,
              personalizedMessage: editResponse.data.personalizedMessage,
              conflicts: editResponse.data.conflicts || [],
              timestamp: editResponse.data.timestamp
            };

            setGeneratedMealPlan(updatedMealPlan);

            // Format the meal plan for display
            const formattedMealPlan = formatMealPlanForDisplay(updatedMealPlan);

            const updatedMessage = {
              id: Date.now() + 1,
              text: `${updatedMealPlan.personalizedMessage}\n\n${formattedMealPlan}\n\nWhat would you like to do with this updated meal plan?`,
              isUser: false,
              timestamp: new Date(),
              showMealPlanActions: true,
            };
            setMessages(prev => [...prev, updatedMessage]);
            setIsEditingMode(false);
            return;
          } else {
            throw new Error(editResponse.data.message || 'Failed to edit meal plan');
          }
        } catch (error) {
          console.error('Error editing meal plan:', error);
          const errorMessage = {
            id: Date.now() + 1,
            text: "I'm sorry, I had trouble updating your meal plan. This could be because the meal you requested isn't available in our database. Please try with a different meal name or be more specific about your request. For example: 'Replace Pinakbet with Adobong Manok'.",
            isUser: false,
            timestamp: new Date(),
          };
          setMessages(prev => [...prev, errorMessage]);
          return;
        }
      }

      const response = await aiAPI.chat({
        message: messageText,
        includeProfile: true,
        includeMeals: true
      });

      if (response.data.success) {
        const aiMessage = {
          id: Date.now() + 1,
          text: response.data.response,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, aiMessage]);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage = {
        id: Date.now() + 1,
        text: "I'm sorry, I'm having trouble responding right now. Please try again later.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const resetChat = () => {
    setMessages([]);
    setSelectedGoal(null);
    setSelectedHealthCondition(null);
    setShowGoalSelection(true);
    setShowChatInput(false);
    addWelcomeMessage();
  };

  const renderMessage = (message) => (
    <View key={message.id} style={[
      styles.messageContainer,
      message.isUser ? styles.userMessage : styles.aiMessage
    ]}>
      <Text style={[
        styles.messageText,
        message.isUser ? styles.userMessageText : styles.aiMessageText
      ]}>
        {message.text}
      </Text>
      <Text style={styles.timestamp}>
        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
      </Text>

      {message.showHealthConditions && (
        <View style={styles.optionsContainer}>
          {healthConditions.map(condition => (
            <TouchableOpacity
              key={condition.id}
              style={styles.optionButton}
              onPress={() => selectHealthCondition(condition)}
            >
              <Text style={styles.optionButtonText}>{condition.name}</Text>
              <Text style={styles.optionDescription}>{condition.description}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {message.showMealPlanActions && generatedMealPlan && (
        <View style={styles.optionsContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              const userMessage = {
                id: Date.now(),
                text: "Save to calendar",
                isUser: true,
                timestamp: new Date(),
              };
              setMessages(prev => [...prev, userMessage]);

              const aiMessage = {
                id: Date.now() + 1,
                text: "Perfect! Please select the date you'd like to schedule this meal plan:",
                isUser: false,
                timestamp: new Date(),
                showDatePicker: true,
              };
              setMessages(prev => [...prev, aiMessage]);
              setShowDatePicker(true);
            }}
          >
            <Text style={styles.actionButtonText}>Save to Calendar</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.editActionButton]}
            onPress={() => {
              const userMessage = {
                id: Date.now(),
                text: "Edit this meal plan",
                isUser: true,
                timestamp: new Date(),
              };
              setMessages(prev => [...prev, userMessage]);

              setIsEditingMode(true);
              const aiMessage = {
                id: Date.now() + 1,
                text: "Great! I can help you edit your meal plan. Please tell me which dishes you'd like to change. For example, you can say 'Add Adobong kangkong as breakfast instead of Pinakbet' or 'Replace the lunch with Sinigang na baboy'.",
                isUser: false,
                timestamp: new Date(),
              };
              setMessages(prev => [...prev, aiMessage]);
              setShowChatInput(true);
            }}
          >
            <Text style={styles.editActionButtonText}>Edit/Update</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryActionButton]}
            onPress={() => {
              const userMessage = {
                id: Date.now(),
                text: "No thanks",
                isUser: true,
                timestamp: new Date(),
              };
              setMessages(prev => [...prev, userMessage]);

              const aiMessage = {
                id: Date.now() + 1,
                text: "No problem! Is there anything else I can help you with regarding your meal planning?",
                isUser: false,
                timestamp: new Date(),
              };
              setMessages(prev => [...prev, aiMessage]);
              setGeneratedMealPlan(null);
              setIsEditingMode(false);
            }}
          >
            <Text style={styles.secondaryActionButtonText}>No Thanks</Text>
          </TouchableOpacity>
        </View>
      )}

      {message.showDatePicker && showDatePicker && (
        <View style={styles.datePickerContainer}>
          <DateTimePicker
            value={selectedDate}
            mode="date"
            display="default"
            onChange={(event, date) => {
              if (date) {
                setSelectedDate(date);
                handleSaveMealPlan(date);
                setShowDatePicker(false);
              }
            }}
            minimumDate={new Date()}
          />
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>AI Meal Assistant</Text>
        <TouchableOpacity onPress={resetChat}>
          <Ionicons name="refresh" size={24} color={colors.surface} />
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView 
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          onContentSizeChange={() => scrollViewRef.current?.scrollToEnd({ animated: true })}
        >
          {messages.map(renderMessage)}
          
          {/* Goal Selection */}
          {showGoalSelection && messages.length > 0 && (
            <View style={styles.goalSelectionContainer}>
              <Text style={styles.goalSelectionTitle}>Select your goal:</Text>
              {goals.map(goal => (
                <TouchableOpacity
                  key={goal.id}
                  style={styles.goalButton}
                  onPress={() => selectGoal(goal)}
                >
                  <Text style={styles.goalButtonText}>{goal.name}</Text>
                  <Text style={styles.goalDescription}>{goal.description}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
          
          {loading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
              <Text style={styles.loadingText}>AI is thinking...</Text>
            </View>
          )}
        </ScrollView>

        {/* Input - Only show when chat input is enabled */}
        {showChatInput && (
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.textInput}
              placeholder="Ask me about meals, nutrition, or dietary advice..."
              value={inputText}
              onChangeText={setInputText}
              multiline
              maxLength={500}
              placeholderTextColor={colors.textSecondary}
            />
            <TouchableOpacity
              style={[styles.sendButton, !inputText.trim() && styles.sendButtonDisabled]}
              onPress={sendMessage}
              disabled={!inputText.trim() || loading}
            >
              <Ionicons name="send" size={20} color={colors.surface} />
            </TouchableOpacity>
          </View>
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  messagesContainer: {
    flex: 1,
    padding: spacing.md,
  },
  messageContainer: {
    marginBottom: spacing.md,
    maxWidth: '80%',
  },
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: colors.primary,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
  },
  aiMessage: {
    alignSelf: 'flex-start',
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  messageText: {
    fontSize: fonts.sizes.medium,
    lineHeight: 20,
  },
  userMessageText: {
    color: colors.surface,
  },
  aiMessageText: {
    color: colors.text,
  },
  timestamp: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    opacity: 0.7,
  },
  goalSelectionContainer: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginTop: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  goalSelectionTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
  },
  goalButton: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
  },
  goalButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  goalDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
  },
  optionsContainer: {
    marginTop: spacing.md,
  },
  optionButton: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
  },
  optionButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  optionDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.md,
  },
  loadingText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginLeft: spacing.sm,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: spacing.md,
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  textInput: {
    flex: 1,
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: fonts.sizes.medium,
    color: colors.text,
    maxHeight: 100,
    marginRight: spacing.sm,
  },
  sendButton: {
    backgroundColor: colors.primary,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: colors.textSecondary,
    opacity: 0.5,
  },
  actionButton: {
    backgroundColor: colors.primary,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.sm,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.surface,
  },
  secondaryActionButton: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
  },
  secondaryActionButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  editActionButton: {
    backgroundColor: colors.warning || '#FF9500',
    borderWidth: 1,
    borderColor: colors.warning || '#FF9500',
  },
  editActionButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.surface,
  },
  datePickerContainer: {
    marginTop: spacing.md,
    alignItems: 'center',
  },
});

export default AIChatScreen;
