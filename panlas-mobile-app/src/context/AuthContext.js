import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { authAPI } from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  // Check if user is logged in on app start
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = await AsyncStorage.getItem('token');
      const userData = await AsyncStorage.getItem('user');
      
      if (token && userData) {
        setUser(JSON.parse(userData));
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials, otpData = null) => {
    try {
      let response;

      if (otpData) {
        // If OTP data is provided, use it directly (from OTP verification)
        response = { data: otpData };
      } else {
        // Normal login flow
        console.log('🔐 Attempting login with credentials:', { email: credentials.email });
        response = await authAPI.login(credentials);
        console.log('🔐 Login API response:', response);

        // Check if email verification is required
        if (response.data.emailVerified === false) {
          return {
            success: false,
            error: response.data.message || 'Email verification required',
            emailVerificationRequired: true,
            email: credentials.email
          };
        }
      }

      const { token, user: userData, requiresTermsAcceptance } = response.data;

      // Validate token and user data before storing
      if (!token) {
        console.error('Login response missing token:', response.data);
        throw new Error('Login response missing authentication token');
      }

      if (!userData) {
        console.error('Login response missing user data:', response.data);
        throw new Error('Login response missing user data');
      }

      // Check if terms acceptance is required (first login)
      if (requiresTermsAcceptance) {
        // Store token temporarily but don't set authenticated state yet
        await AsyncStorage.setItem('token', token);
        return {
          success: true,
          requiresTermsAcceptance: true,
          data: response.data
        };
      }

      // Store token and user data
      await AsyncStorage.setItem('token', token);
      await AsyncStorage.setItem('user', JSON.stringify(userData));

      setUser(userData);
      setIsAuthenticated(true);

      return { success: true, data: response.data };
    } catch (error) {
      console.error('Login error:', error);

      // Check if it's an email verification error
      if (error.response?.data?.emailVerified === false) {
        return {
          success: false,
          error: error.response.data.message || 'Email verification required',
          emailVerificationRequired: true,
          email: credentials.email
        };
      }

      return {
        success: false,
        error: error.response?.data?.message || 'Login failed'
      };
    }
  };

  const register = async (userData) => {
    try {
      const response = await authAPI.register(userData);

      // Registration successful, but user needs to verify email via OTP
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Registration error:', error);

      // Check if it's a user already exists error
      if (error.response?.status === 409 && error.response?.data?.userExists) {
        return {
          success: false,
          error: error.response.data.message,
          userExists: true,
          redirectToLogin: error.response.data.redirectToLogin,
          existingEmail: error.response.data.existingEmail,
          existingUsername: error.response.data.existingUsername,
          isEmailVerified: error.response.data.isEmailVerified
        };
      }

      return {
        success: false,
        error: error.response?.data?.message || 'Registration failed'
      };
    }
  };

  const completeLoginAfterTerms = async (loginData) => {
    try {
      const { user: userData } = loginData;

      // Store user data and set authenticated state
      await AsyncStorage.setItem('user', JSON.stringify(userData));
      setUser(userData);
      setIsAuthenticated(true);

      return { success: true };
    } catch (error) {
      console.error('Error completing login after terms:', error);
      return { success: false, error: 'Failed to complete login' };
    }
  };

  const logout = async () => {
    try {
      // Call backend logout endpoint first
      const token = await AsyncStorage.getItem('token');
      if (token) {
        try {
          await authAPI.logout();
        } catch (error) {
          console.error('Backend logout failed:', error);
          // Continue with local logout even if backend fails
        }
      }

      // Clear local storage
      await AsyncStorage.removeItem('token');
      await AsyncStorage.removeItem('user');
      setUser(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const updateUser = async (userData) => {
    try {
      const response = await authAPI.updateProfile(userData);
      const updatedUser = response.data.user;

      await AsyncStorage.setItem('user', JSON.stringify(updatedUser));
      setUser(updatedUser);

      return { success: true, data: updatedUser };
    } catch (error) {
      console.error('Update user error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Update failed'
      };
    }
  };

  const refreshUser = async () => {
    try {
      const response = await authAPI.getProfile();
      const userData = response.data;

      await AsyncStorage.setItem('user', JSON.stringify(userData));
      setUser(userData);

      return { success: true, data: userData };
    } catch (error) {
      console.error('Refresh user error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Refresh failed'
      };
    }
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    login,
    register,
    logout,
    updateUser,
    refreshUser,
    completeLoginAfterTerms,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
