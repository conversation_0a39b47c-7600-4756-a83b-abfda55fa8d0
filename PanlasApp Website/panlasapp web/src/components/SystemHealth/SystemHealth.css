
.system-health {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.health-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.health-header h2 {
  margin: 0;
}

.refresh-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-controls select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.refresh-button {
  padding: 0.25rem 0.75rem;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.refresh-button:hover {
  background-color: #45a049;
}

.refresh-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.last-refreshed {
  margin-bottom: 1.5rem;
  color: #666;
  font-size: 0.9rem;
  font-style: italic;
}

.health-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.health-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.health-card h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.1rem;
}

.health-value {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.health-detail {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.progress-bar {
  height: 10px;
  background-color: #e0e0e0;
  border-radius: 5px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.progress {
  height: 100%;
  transition: width 0.3s ease;
}

.status-good {
  color: #4CAF50;
}

.progress.status-good {
  background-color: #4CAF50;
}

.status-warning {
  color: #FF9800;
}

.progress.status-warning {
  background-color: #FF9800;
}

.status-danger {
  color: #F44336;
}

.progress.status-danger {
  background-color: #F44336;
}

.loading-health, .error-health {
  text-align: center;
  padding: 2rem;
}

.error-health {
  color: #F44336;
}

@media (max-width: 768px) {
  .health-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .health-grid {
    grid-template-columns: 1fr;
  }
}
