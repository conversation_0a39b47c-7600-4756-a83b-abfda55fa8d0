import React from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

function SignupChart({ monthlyStats }) {
  // If no data is provided, return null
  if (!monthlyStats || monthlyStats.length === 0) {
    return null;
  }

  // Prepare data for the chart
  const labels = monthlyStats.map(stat => stat.label);
  const data = monthlyStats.map(stat => stat.count);

  const chartData = {
    labels,
    datasets: [
      {
        label: 'User Signups',
        data,
        fill: true,
        backgroundColor: 'rgba(32, 197, 175, 0.1)',
        borderColor: '#20C5AF',
        borderWidth: 3,
        tension: 0.4,
        pointBackgroundColor: '#20C5AF',
        pointBorderColor: '#fff',
        pointBorderWidth: 3,
        pointRadius: 6,
        pointHoverRadius: 8,
        pointHoverBackgroundColor: '#1ba896',
        pointHoverBorderColor: '#fff',
        pointHoverBorderWidth: 3,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          color: '#2c3e50',
          font: {
            size: 14,
            weight: '600',
          },
          padding: 20,
          usePointStyle: true,
          pointStyle: 'circle',
        },
      },
      title: {
        display: true,
        text: 'Monthly User Signups',
        color: '#2c3e50',
        font: {
          size: 18,
          weight: '700',
        },
        padding: {
          top: 10,
          bottom: 30,
        },
      },
    },
    scales: {
      x: {
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
          borderColor: '#e9ecef',
        },
        ticks: {
          color: '#6c757d',
          font: {
            size: 12,
            weight: '500',
          },
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
          borderColor: '#e9ecef',
        },
        ticks: {
          precision: 0,
          color: '#6c757d',
          font: {
            size: 12,
            weight: '500',
          },
        },
      },
    },
    elements: {
      point: {
        hoverRadius: 8,
      },
    },
    interaction: {
      intersect: false,
      mode: 'index',
    },
  };

  return (
    <div className="chart-container" style={{ height: '400px', width: '100%' }}>
      <Line data={chartData} options={options} />
    </div>
  );
}

export default SignupChart;
