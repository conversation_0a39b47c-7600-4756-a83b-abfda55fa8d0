//Landing Page
import React from "react";
import { Link } from "react-router-dom";
import './Landing.css'
import LandingHeader from "./LandingHeader";
import Footer from "../Footer/Footer";
const Landing = () => {
  return (
    <div className="landing-page">
      <LandingHeader />
      
      <div className="hero-section">
        <div className="container">
          <h1>Family Meal Planner</h1>
          <p className="tagline">Plan, prepare, and enjoy meals together with ease</p>
          <div className="cta-buttons">
            <Link to="/signup" className="btn btn-primary">Get Started</Link>
          </div>
        </div>
      </div>

      <div className="main-content">
        <div className="container">
          <div className="features-section">
            <h2>Why Choose Our Meal Planner?</h2>
            <div className="features-grid">
              <div className="feature-card">
                <div className="feature-icon">📅</div>
                <h3>Weekly Planning</h3>
                <p>Organize your family meals for the entire week in minutes</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">🥗</div>
                <h3>Recipe Collection</h3>
                <p>Access hundreds of family-friendly recipes or add your own</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">👨‍👩‍👧‍👦</div>
                <h3>Family Preferences</h3>
                <p>Track dietary needs and preferences for each family member</p>
              </div>
            </div>
          </div>

<div className="how-it-works">
  <h2>Features</h2>
  <div className="steps">
    <div className="step">
      <div className="step-number">1</div>
      <div className="step-content">
        <h3>Create Your Own /Family Profile</h3>
        <p>Add family members and their dietary preferences</p>
      </div>
    </div>
    <div className="step">
      <div className="step-number">2</div>
      <div className="step-content">
        <h3>Browse Recipes</h3>
        <p>Explore our collection or Meal favorites</p>
      </div>
    </div>
    <div className="step">
      <div className="step-number">3</div>
      <div className="step-content">
        <h3>Plan Your Week</h3>
        <p>Select meals and plan your weekly calendar</p>
      </div>
    </div>
    <div className="step">
      <div className="step-number">4</div>
      <div className="step-content">
        <h3>Cook</h3>
        <p>Use generated Ingredients and Steps</p>
      </div>
    </div>
  </div>
</div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Landing;
  