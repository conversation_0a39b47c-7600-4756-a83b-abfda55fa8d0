import React from 'react';
import '../../styles/Auth.css';

const TermsModal = ({ isOpen, onAccept, onDecline }) => {
  console.log('📋 TermsModal render - isOpen:', isOpen);

  if (!isOpen) return null;

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      console.log('🚫 Terms modal overlay clicked - declining');
      onDecline();
    }
  };

  const handleAcceptClick = () => {
    console.log('✅ Terms accept button clicked in modal');
    onAccept();
  };

  const handleDeclineClick = () => {
    console.log('❌ Terms decline button clicked in modal');
    onDecline();
  };

  return (
    <div className="terms-modal-overlay" onClick={handleOverlayClick}>
      <div className="terms-modal">
        <div className="terms-modal-header">
          <h2>Terms and Conditions & Data Privacy</h2>
          <p>Please read and accept our terms to continue</p>
        </div>
        
        <div className="terms-modal-content">
          <h3>Terms and Conditions</h3>
          <p>
            By using PanlasApp, you agree to the following terms and conditions:
          </p>
          <ul>
            <li>You will use the app responsibly and in accordance with applicable laws</li>
            <li>You are responsible for maintaining the confidentiality of your account</li>
            <li>You will provide accurate and up-to-date information</li>
            <li>You understand that meal recommendations are for informational purposes only</li>
            <li>You will not misuse the app or attempt to harm its functionality</li>
          </ul>

          <h3>Data Privacy Policy</h3>
          <p>
            We are committed to protecting your privacy and personal information:
          </p>
          <ul>
            <li><strong>Data Collection:</strong> We collect only necessary information to provide our services, including your name, email, dietary preferences, and family information</li>
            <li><strong>Data Usage:</strong> Your data is used to personalize meal recommendations, manage your family profiles, and improve our services</li>
            <li><strong>Data Sharing:</strong> We do not sell or share your personal information with third parties without your consent</li>
            <li><strong>Data Security:</strong> We implement appropriate security measures to protect your information</li>
            <li><strong>Data Retention:</strong> We retain your data only as long as necessary to provide our services</li>
            <li><strong>Your Rights:</strong> You have the right to access, update, or delete your personal information at any time</li>
          </ul>

          <h3>Contact Information</h3>
          <p>
            If you have any questions about these terms or our privacy practices, please contact us at:
          </p>
          <p>
            <strong>Email:</strong> <EMAIL><br />
            <strong>Phone:</strong> Tel: 8-4248370
          </p>

          <p>
            <strong>Last Updated:</strong> {new Date().toLocaleDateString()}
          </p>
        </div>
        
        <div className="terms-modal-actions">
          <button
            className="terms-modal-button decline"
            onClick={handleDeclineClick}
          >
            Decline
          </button>
          <button
            className="terms-modal-button accept"
            onClick={handleAcceptClick}
          >
            Accept & Continue
          </button>
        </div>
      </div>
    </div>
  );
};

export default TermsModal;
