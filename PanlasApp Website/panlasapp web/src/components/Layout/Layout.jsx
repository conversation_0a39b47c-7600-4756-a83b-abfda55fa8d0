import React, { useState } from "react";
import Header from "../Header/Header";
import Sidebar from "../Sidebar/Sidebar";

const Layout = ({ children }) => {
  const [sidebarActive, setSidebarActive] = useState(false);
  
  const toggleSidebar = () => {
    setSidebarActive(!sidebarActive);
  };
  
  return (
    <div className="app-container">
      <Header toggleSidebar={toggleSidebar} />
      <Sidebar isActive={sidebarActive} />
      <div className="content-area">
        {children}
      </div>
    </div>
  );
};

export default Layout;
