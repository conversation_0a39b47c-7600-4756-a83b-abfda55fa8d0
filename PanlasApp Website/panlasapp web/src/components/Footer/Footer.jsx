import React from "react";
import { Link } from "react-router-dom";
import './Footer.css'
const Footer = () => {
  return (
    <footer className="site-footer">
      <div className="container">
        <div className="footer-content">
          <div className="footer-section about">
            <h3>Family Meal Planner</h3>
            <p>Making family meal planning simple, efficient, and enjoyable.</p>
            <div className="social-links">
              <a href="#" aria-label="Facebook"><i className="fab fa-facebook"></i></a>
              <a href="#" aria-label="Twitter"><i className="fab fa-twitter"></i></a>
              <a href="#" aria-label="Instagram"><i className="fab fa-instagram"></i></a>
              <a href="#" aria-label="Pinterest"><i className="fab fa-pinterest"></i></a>
            </div>
          </div>
          
          <div className="footer-section links">
            <h3>Quick Links</h3>
            <ul>
              <li><Link to="/">Home</Link></li>
              <li><Link to="/meal-plan">Meal Plan</Link></li>
              <li><Link to="/family">Family</Link></li>
              <li><Link to="/favorites">Favorites</Link></li>
              <li><Link to="/help-center">Help Center</Link></li>
            </ul>
          </div>
          
          <div className="footer-section contact">
            <h3>Contact Us</h3>
            <p><i className="fas fa-envelope"></i> <EMAIL></p>
            <p><i className="fas fa-phone"></i> Tel: 8-4248370</p>
          </div>
        </div>
        
        <div className="footer-bottom">
          <p>&copy; {new Date().getFullYear()} Family Meal Planner. All rights reserved.</p>
          <div className="footer-bottom-links">
            <Link to="/privacy">Privacy Policy</Link>
            <Link to="/terms">Terms of Service</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
