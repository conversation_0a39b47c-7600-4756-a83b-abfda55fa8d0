import React, { useState, useEffect } from 'react';
import { FaHeart, FaRegHeart, FaStar, FaLeaf, FaFire, FaClock, FaChevronRight, FaUsers } from 'react-icons/fa';
import axios from 'axios';
import userAPI from '../../services/userAPI';
import { useFavorites } from '../Favorites/FavoritesContext';
import './RecommendedMeals.css';

const RecommendedMeals = ({ onMealClick }) => {
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [appliedFilters, setAppliedFilters] = useState({});
  const { favorites, addFavorite, removeFavorite, isFavorite } = useFavorites();

  useEffect(() => {
    loadRecommendations();
  }, []);

  const loadRecommendations = async () => {
    try {
      setLoading(true);
      setError('');
      
      const token = localStorage.getItem('token');
      if (!token) {
        setError('Please log in to see personalized recommendations');
        setLoading(false);
        return;
      }

      console.log('🔄 Loading personalized meal recommendations...');
      
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await axios.get(`${API_BASE_URL}/meals/recommendations`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params: {
          includeFamily: 'true', // Include family preferences
          limit: 8 // Get 8 recommendations
        }
      });

      console.log('📊 Recommendations response:', response.data);
      
      if (response.data.success) {
        setRecommendations(response.data.recommendations || []);
        setAppliedFilters(response.data.appliedFilters || {});
        console.log(`✅ Loaded ${response.data.recommendations?.length || 0} recommendations`);
      } else {
        setError('Failed to load recommendations');
      }
    } catch (err) {
      console.error('❌ Error loading recommendations:', err);
      setError('Failed to load personalized recommendations');
      
      // Fallback: Load some popular meals
      try {
        const fallbackResponse = await axios.get(`${API_BASE_URL}/meals/popular/list`);
        if (fallbackResponse.data) {
          setRecommendations(fallbackResponse.data.slice(0, 6));
          console.log('📋 Using fallback popular meals');
        }
      } catch (fallbackErr) {
        console.error('❌ Fallback also failed:', fallbackErr);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleFavoriteToggle = async (meal) => {
    try {
      if (isFavorite(meal._id)) {
        await removeFavorite(meal._id);
      } else {
        await addFavorite(meal);
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  const formatRecommendationReasons = (reasons) => {
    if (!reasons || reasons.length === 0) return 'Recommended for you';
    return reasons.slice(0, 2).join(' • ');
  };

  const getDietaryBadges = (meal) => {
    const badges = [];
    const dietType = meal.dietType || {};
    
    if (dietType.isVegan) badges.push({ label: 'Vegan', color: '#4CAF50', icon: '🌱' });
    else if (dietType.isVegetarian) badges.push({ label: 'Vegetarian', color: '#8BC34A', icon: '🥬' });
    if (dietType.isGlutenFree) badges.push({ label: 'Gluten-Free', color: '#FF9800', icon: '🌾' });
    if (dietType.isHalal) badges.push({ label: 'Halal', color: '#2196F3', icon: '☪️' });
    if (dietType.isLowCarb) badges.push({ label: 'Low-Carb', color: '#9C27B0', icon: '🥩' });
    
    return badges.slice(0, 2); // Show max 2 badges
  };

  if (loading) {
    return (
      <div className="recommended-meals-container">
        <div className="recommended-meals-header">
          <h2>Recommended for You</h2>
          <p>Loading personalized recommendations...</p>
        </div>
        <div className="recommendations-loading">
          <div className="loading-spinner"></div>
        </div>
      </div>
    );
  }

  if (error && recommendations.length === 0) {
    return (
      <div className="recommended-meals-container">
        <div className="recommended-meals-header">
          <h2>Recommended for You</h2>
          <p className="error-text">{error}</p>
        </div>
      </div>
    );
  }

  if (recommendations.length === 0) {
    return (
      <div className="recommended-meals-container">
        <div className="recommended-meals-header">
          <h2>🎯 Recommended for You</h2>
          <p>No recommendations available. Try setting your dietary preferences!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="recommended-meals-container">
      <div className="recommended-meals-header">
        <div className="header-content">
          <h2>Recommended for Your Family</h2>
          <p>Personalized meal suggestions based on your family preferences and dietary needs</p>
        </div>
        {appliedFilters && Object.keys(appliedFilters).length > 0 && (
          <div className="applied-filters">
            <div className="filters-header">
              <span className="filters-title">
                <FaUsers /> Family Dietary Preferences Applied
              </span>
              {appliedFilters.includeFamily && (
                <span className="family-indicator">
                  Including all family members
                </span>
              )}
            </div>
            <div className="filters-content">
              {appliedFilters.restrictions?.length > 0 && (
                <div className="filter-group">
                  <span className="filter-tag dietary">
                    <FaLeaf /> Dietary: {appliedFilters.restrictions.join(', ')}
                  </span>
                </div>
              )}
              {appliedFilters.allergies?.length > 0 && (
                <div className="filter-group">
                  <span className="filter-tag allergy">
                    ⚠️ Avoiding: {appliedFilters.allergies.join(', ')}
                  </span>
                </div>
              )}
              {appliedFilters.calorieTarget && (
                <div className="filter-group">
                  <span className="filter-tag calorie">
                    🎯 Target: {appliedFilters.calorieTarget} cal/day
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      <div className="recommendations-grid">
        {recommendations.map((meal, index) => (
          <div
            key={meal._id || index}
            className="recommendation-card"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className="recommendation-image">
              {meal.image ? (
                <img src={meal.image} alt={meal.name} />
              ) : (
                <div className="meal-placeholder">🍽️</div>
              )}
              <button
                className={`favorite-btn ${isFavorite(meal._id) ? 'favorited' : ''}`}
                onClick={() => handleFavoriteToggle(meal)}
              >
                {isFavorite(meal._id) ? <FaHeart /> : <FaRegHeart />}
              </button>
              {meal.recommendationScore && (
                <div className="recommendation-score">
                  <FaStar /> {Math.round(meal.recommendationScore)}
                </div>
              )}
            </div>

            <div className="recommendation-content">
              <h3 className="meal-name">{meal.name}</h3>
              
              <div className="meal-meta">
                <div className="meal-stats">
                  <span className="calories">
                    <FaFire /> {meal.calories} cal
                  </span>
                  {meal.prepTime && (
                    <span className="prep-time">
                      <FaClock /> {meal.prepTime}m
                    </span>
                  )}
                  {meal.rating && (
                    <span className="rating">
                      <FaStar /> {meal.rating}
                    </span>
                  )}
                </div>
              </div>

              {getDietaryBadges(meal).length > 0 && (
                <div className="dietary-badges">
                  {getDietaryBadges(meal).map((badge, idx) => (
                    <span 
                      key={idx} 
                      className="dietary-badge"
                      style={{ backgroundColor: badge.color }}
                    >
                      {badge.icon} {badge.label}
                    </span>
                  ))}
                </div>
              )}

              <p className="meal-description">
                {meal.description?.substring(0, 80)}
                {meal.description?.length > 80 ? '...' : ''}
              </p>

              <div className="recommendation-reason">
                <span className="reason-text">
                  {formatRecommendationReasons(meal.recommendationReasons)}
                </span>
              </div>

              <button 
                className="view-meal-btn"
                onClick={() => onMealClick && onMealClick(meal)}
              >
                View Recipe <FaChevronRight />
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Removed the recommendations-footer and refresh button */}
    </div>
  );
};

export default RecommendedMeals;
