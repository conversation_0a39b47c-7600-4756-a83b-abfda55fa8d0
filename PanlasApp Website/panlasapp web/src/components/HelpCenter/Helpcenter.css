/* ===== HELP CENTER - MODERN DESIGN ===== */
.help-center-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
  padding: var(--space-8);
  border-bottom: 2px solid var(--border-light);
  background: var(--bg-primary);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  box-shadow: var(--shadow-sm);
}

.help-center-header h1 {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-4xl);
  font-weight: 700;
  letter-spacing: -0.5px;
  font-family: var(--font-family);
}

.feedback-button {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  outline: none;
  font-family: var(--font-family);
}

.feedback-button:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.feedback-button:hover,
.feedback-button:active {
  background: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.feedback-success-message {
  background: var(--success-light);
  color: var(--success-color);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-8);
  border-left: 4px solid var(--success-color);
  font-weight: 600;
  animation: slideDown 0.3s ease-out;
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  box-shadow: var(--shadow-sm);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.help-content {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: 0 var(--space-6);
  box-sizing: border-box;
}

@media (max-width: 1200px) {
  .help-content {
    max-width: 98vw;
    padding-left: 2vw;
    padding-right: 2vw;
  }
}
@media (max-width: 900px) {
  .help-content {
    max-width: 100vw;
    padding-left: 1vw;
    padding-right: 1vw;
  }
  .help-section {
    padding: 1.5rem 1rem;
  }
}
@media (max-width: 768px) {
  .help-content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
.help-section {
  margin-bottom: var(--space-8);
  background: var(--bg-primary);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  transition: all 0.2s ease;
}

.help-section:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.help-section h2 {
  color: var(--primary-color);
  margin-top: 0;
  margin-bottom: var(--space-5);
  font-size: var(--font-size-2xl);
  font-weight: 700;
  letter-spacing: -0.5px;
  font-family: var(--font-family);
}

.help-section h3 {
  color: var(--text-primary);
  margin-top: 0;
  margin-bottom: var(--space-5);
  font-size: var(--font-size-xl);
  font-weight: 600;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: var(--space-2);
  font-family: var(--font-family);
}

.help-section p,
.help-section ul {
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: var(--space-4);
  font-size: var(--font-size-base);
  font-family: var(--font-family);
}

.help-section ul {
  padding-left: var(--space-6);
}

.help-section li {
  margin-bottom: var(--space-2);
  color: var(--text-secondary);
}

.help-item {
  margin-bottom: var(--space-6);
  padding: var(--space-5);
  background: var(--primary-light);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--primary-color);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.help-item:hover {
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}

.help-item:last-child {
  margin-bottom: 0;
}

.help-item h4 {
  color: var(--text-primary);
  margin-top: 0;
  margin-bottom: var(--space-3);
  font-size: var(--font-size-lg);
  font-weight: 600;
  font-family: var(--font-family);
}

.help-item p {
  margin-bottom: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  line-height: 1.6;
}

.dietary-preferences-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1.1rem;
  margin: 1.5rem 0;
}

.dietary-preference-card {
  background: #fffbe7;
  border: 1px solid #ffe0b2;
  border-radius: 10px;
  padding: 1rem 1.5rem;
  min-width: 210px;
  max-width: 320px;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.08);
  margin-bottom: 0.5rem;
  display: flex;
  flex-direction: column;
}

.dietary-preference-name {
  font-weight: bold;
  color: #20C5AF;
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
}

.dietary-preference-desc {
  color: #555;
  font-size: 0.97rem;
}

/* Responsive Design */
@media (max-width: 900px) {
  .help-content {
    max-width: 98vw;
    padding: 0 1vw;
  }
  .help-section {
    padding: 1.5rem 1rem;
  }
}

@media (max-width: 768px) {
  .help-center-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
    padding-bottom: 0.5rem;
  }

  .help-center-header h1 {
    font-size: 2rem;
  }

  .feedback-button {
    align-self: stretch;
    justify-content: center;
    font-size: 1rem;
    padding: 0.7rem 1.2rem;
  }

  .help-section {
    padding: 1.1rem 0.7rem;
    margin-bottom: 1.5rem;
    border-radius: 10px;
  }

  .help-section h2 {
    font-size: 1.2rem;
  }

  .help-section h3 {
    font-size: 1.05rem;
    padding-bottom: 0.3rem;
  }

  .help-item {
    padding: 0.8rem 0.6rem;
  }

  .help-item h4 {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .help-center-header h1 {
    font-size: 1.3rem;
  }

  .feedback-button {
    padding: 0.5rem 0.8rem;
    font-size: 0.92rem;
  }

  .help-section {
    padding: 0.7rem 0.3rem;
    border-radius: 7px;
  }

  .help-section h2 {
    font-size: 1rem;
  }

  .help-section h3 {
    font-size: 0.95rem;
  }

  .help-item {
    padding: 0.5rem 0.3rem;
    margin-bottom: 1rem;
  }

  .help-item h4 {
    font-size: 0.92rem;
  }
}
