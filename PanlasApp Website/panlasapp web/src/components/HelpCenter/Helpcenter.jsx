import React, { useState } from "react";
import { Link } from "react-router-dom";
import '../../../src/App.css';
import Header from "../Header/Header";
import Layout from "../Layout/Layout";
import FeedbackModal from "../Feedback/FeedbackModal";
import './Helpcenter.css';

// Dietary preferences with descriptions
const dietaryPreferencesInfo = [
  { name: 'Vegetarian', description: 'No meat, poultry, or fish. May include dairy and eggs.' },
  { name: 'Vegan', description: 'No animal products at all, including dairy, eggs, and honey.' },
  { name: 'Dairy-Free', description: 'No milk or milk-derived ingredients.' },
  { name: 'Egg-Free', description: 'No eggs or foods containing eggs.' },
  { name: 'Gluten-Free', description: 'No wheat, barley, rye, or foods containing gluten.' },
  { name: 'Soy-Free', description: 'No soybeans or soy-derived ingredients.' },
  { name: 'Nut-Free', description: 'No peanuts or tree nuts.' },
  { name: 'Low-Carb', description: 'Reduced carbohydrate intake.' },
  { name: 'Low-Sugar', description: 'Reduced sugar intake.' },
  { name: 'Sugar-Free', description: 'No added sugars or natural sugars.' },
  { name: 'Low-Fat', description: 'Reduced fat intake.' },
  { name: 'Low-Sodium', description: 'Reduced salt/sodium intake.' },
  { name: 'Organic', description: 'Foods grown without synthetic pesticides or fertilizers.' },
  { name: 'Halal', description: 'Meets Islamic dietary laws.' },
  { name: 'High-Protein', description: 'Increased protein intake.' },
  { name: 'Pescatarian', description: 'No meat or poultry, but includes fish and seafood.' },
  { name: 'Keto', description: 'Very low-carb, high-fat diet.' },
  { name: 'Plant-Based', description: 'Primarily foods from plants, may include some animal products.' },
  { name: 'Kosher', description: 'Meets Jewish dietary laws.' },
  { name: 'Climatarian', description: 'Focuses on foods with low environmental impact.' },
  { name: 'Raw Food', description: 'Mostly or entirely uncooked and unprocessed foods.' },
  { name: 'Mediterranean', description: 'Emphasizes fruits, vegetables, fish, olive oil, and whole grains.' },
  { name: 'Paleo', description: 'Foods presumed to be eaten by early humans; excludes processed foods, grains, and dairy.' },
  { name: 'Kangatarian', description: 'Vegetarian diet that includes kangaroo meat.' },
  { name: 'Pollotarian', description: 'Vegetarian diet that includes poultry.' },
  { name: 'Flexitarian', description: 'Primarily plant-based, but occasionally includes meat, fish, or poultry.' },
];

const Helpcenter = () => {
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);

  const handleFeedbackSubmit = async (feedbackData) => {
    try {
      const token = localStorage.getItem('token');
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await fetch(`${API_BASE_URL}/feedback/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify(feedbackData)
      });

      if (response.ok) {
        setFeedbackSubmitted(true);
        setTimeout(() => setFeedbackSubmitted(false), 3000);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to submit feedback');
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      throw error;
    }
  };

  return (
    <Layout>
      <div className="main-content">

          <div className="help-center-header">
            <h1>HELP CENTER</h1>
            <button
              className="feedback-button"
              onClick={() => setShowFeedbackModal(true)}
            >
              📝 Send Feedback
            </button>
          </div>

          {feedbackSubmitted && (
            <div className="feedback-success-message">
              ✅ Thank you for your feedback! We appreciate your input.
            </div>
          )}

          <div className="help-content">
            <section className="help-section">
              <h2>Welcome to PanlasApp Help Center</h2>
              <p>Find answers to common questions and get help with using PanlasApp for your meal planning needs.</p>
            </section>

            <section className="help-section">
              <h3>Dietary Preferences Guide</h3>
              <p>
                <strong>Not sure what a dietary preference means?</strong> Here’s a quick guide to help you choose the best options for your needs:
              </p>
              <div className="dietary-preferences-list">
                {dietaryPreferencesInfo.map((pref) => (
                  <div className="dietary-preference-card" key={pref.name}>
                    <span className="dietary-preference-name">{pref.name}</span>
                    <span className="dietary-preference-desc">{pref.description}</span>
                  </div>
                ))}
              </div>
            </section>

            <section className="help-section">
              <h3>Getting Started</h3>
              <div className="help-item">
                <h4>How to create your first meal plan?</h4>
                <p>Navigate to the Meal Plan section and start by selecting your preferred meals for each day. You can customize your plan based on your dietary preferences and family size.</p>
              </div>
              <div className="help-item">
                <h4>Setting up your dietary preferences</h4>
                <p>Go to your Profile section to set up dietary restrictions, allergies, and nutritional goals. This helps us provide better meal recommendations.</p>
              </div>
            </section>

            <section className="help-section">
              <h3>Meal Planning Features</h3>
              <div className="help-item">
                <h4>How to save favorite meals?</h4>
                <p>Click the heart icon on any meal card to add it to your favorites. Access your saved meals from the Favorites section.</p>
              </div>
              <div className="help-item">
                <h4>Family meal planning</h4>
                <p>Add family members in the Family section and set individual dietary preferences for each member to create personalized meal plans.</p>
              </div>
            </section>

            <section className="help-section">
              <h3>Account Management</h3>
              <div className="help-item">
                <h4>How to update your profile?</h4>
                <p>Visit the Profile section to update your personal information, dietary preferences, and account settings.</p>
              </div>
              <div className="help-item">
                <h4>Changing your password</h4>
                <p>In your Profile section, you'll find an option to change your password securely.</p>
              </div>
            </section>

            <section className="help-section">
              <h3>Need More Help?</h3>
              <p>Can't find what you're looking for? We'd love to hear from you! Use the feedback button above to:</p>
              <ul>
                <li>Report bugs or technical issues</li>
                <li>Request new features</li>
                <li>Share your experience with PanlasApp</li>
                <li>Ask questions not covered in this help center</li>
                <li>Suggest improvements to our meal recommendations</li>
              </ul>
            </section>
          </div>

      </div>

      <FeedbackModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        onSubmit={handleFeedbackSubmit}
      />
    </Layout>
  );
};

export default Helpcenter;
