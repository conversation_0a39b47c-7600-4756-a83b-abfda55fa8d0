.recent-activity {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.recent-activity h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #333;
}

.activity-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.activity-item {
  display: flex;
  padding: 1rem 0;
  border-bottom: 1px solid #eee;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #f5f5f5;
  border-radius: 50%;
}

.activity-content {
  flex: 1;
}

.activity-user {
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.activity-description {
  color: #555;
  margin-bottom: 0.25rem;
}

.activity-time {
  font-size: 0.8rem;
  color: #888;
}

.no-activity {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

.loading-activity, .error-activity {
  text-align: center;
  padding: 2rem;
}

.error-activity {
  color: #f44336;
}
