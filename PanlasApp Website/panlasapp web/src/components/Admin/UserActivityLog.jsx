import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './UserActivityLog.css';

function UserActivityLog() {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchActivityLog();
  }, [filter, currentPage]);

  const fetchActivityLog = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      const config = {
        headers: {
          'x-auth-token': token
        },
        params: {
          type: filter !== 'all' ? filter : undefined,
          page: currentPage,
          limit: 10
        }
      };

      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await axios.get(`${API_BASE_URL}/activity/log`, config);
      setActivities(response.data.activities);
      setTotalPages(response.data.totalPages);
      setLoading(false);
    } catch (err) {
      setError('Failed to load activity log');
      console.error('Activity log error:', err);
      setLoading(false);
    }
  };

  const handleFilterChange = (e) => {
    setFilter(e.target.value);
    setCurrentPage(1); // Reset to first page when filter changes
  };

  const handleSearch = (e) => {
    e.preventDefault();
    fetchActivityLog();
  };

  const handlePageChange = (newPage) => {
    if (newPage > 0 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const getActivityTypeLabel = (type) => {
    switch (type) {
      case 'login':
        return 'Login';
      case 'signup':
        return 'Signup';
      case 'create_meal_plan':
        return 'Create Meal Plan';
      case 'update_profile':
        return 'Update Profile';
      case 'delete_meal_plan':
        return 'Delete Meal Plan';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1).replace(/_/g, ' ');
    }
  };

  if (loading && activities.length === 0) return <div className="loading-log">Loading activity log...</div>;
  if (error && activities.length === 0) return <div className="error-log">{error}</div>;

  return (
    <div className="activity-log">
      <h2>User Activity Log</h2>
      
      <div className="log-controls">
        <div className="filter-container">
          <label htmlFor="activity-filter">Filter by type:</label>
          <select 
            id="activity-filter" 
            value={filter} 
            onChange={handleFilterChange}
          >
            <option value="all">All Activities</option>
            <option value="login">Login</option>
            <option value="signup">Signup</option>
            <option value="create_meal_plan">Create Meal Plan</option>
            <option value="update_profile">Update Profile</option>
            <option value="delete_meal_plan">Delete Meal Plan</option>
          </select>
        </div>
        
        <form className="search-container" onSubmit={handleSearch}>
          <input
            type="text"
            placeholder="Search by username..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <button type="submit">Search</button>
        </form>
      </div>
      
      {activities.length === 0 ? (
        <div className="no-activities">No activities found matching your criteria</div>
      ) : (
        <>
          <table className="activity-table">
            <thead>
              <tr>
                <th>User</th>
                <th>Activity Type</th>
                <th>Description</th>
                <th>IP Address</th>
                <th>Timestamp</th>
              </tr>
            </thead>
            <tbody>
              {activities.map((activity, index) => (
                <tr key={index}>
                  <td>{activity.username}</td>
                  <td>
                    <span className={`activity-type ${activity.type}`}>
                      {getActivityTypeLabel(activity.type)}
                    </span>
                  </td>
                  <td>{activity.description}</td>
                  <td>{activity.ipAddress}</td>
                  <td>{new Date(activity.timestamp).toLocaleString()}</td>
                </tr>
              ))}
            </tbody>
          </table>
          
          <div className="pagination">
            <button 
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </button>
            
            <span className="page-info">
              Page {currentPage} of {totalPages}
            </span>
            
            <button 
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </button>
          </div>
        </>
      )}
    </div>
  );
}

export default UserActivityLog;
