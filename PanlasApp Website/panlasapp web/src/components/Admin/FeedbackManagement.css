/* Feedback Management Styles */
.feedback-management {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e0e0e0;
}

.feedback-header h1 {
  margin: 0;
  color: #333;
  font-size: 2.5rem;
  font-weight: 700;
}
.back-button {
  background: none;
  color: black;
  font-weight: bold;
  border: none;
  padding: 0;
  font-size: 1rem;
  cursor: pointer;
  transition: color 0.2s;
}

.back-button:hover {
  color: grey;
}


/* Statistics Cards */
.feedback-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  border: 1px solid #f0f0f0;
}

.stat-card h3 {
  margin: 0 0 0.5rem 0;

  color: #666;

  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-number {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: #333;
}

.stat-number.open {
  color: black;
}

.stat-number.progress {
  color: black;
}

.stat-number.resolved {
  color: black;
}

/* Filters */
.feedback-filters {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.filter-group select {
  padding: 0.5rem;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 0.9rem;
  min-width: 150px;
}

.filter-group select:focus {
  outline: none;
  border-color: #4caf50;
}

/* Table */
.feedback-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.feedback-table {
  width: 100%;
  border-collapse: collapse;
}

.feedback-table th,
.feedback-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.feedback-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.feedback-table tr:hover {
  background: #f8f9fa;
}

/* Tags and Status */
.category-tag,
.priority-tag {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.category-tag.bug_report {
  background: #ffebee;
  color: #c62828;
}

.category-tag.feature_request {
  background: #e3f2fd;
  color: #1565c0;
}

.category-tag.general_feedback {
  background: #f3e5f5;
  color: #7b1fa2;
}

.category-tag.user_experience {
  background: #fff3e0;
  color: #ef6c00;
}

.category-tag.meal_suggestions {
  background: #e8f5e9;
  color: #2e7d32;
}

.category-tag.technical_issue {
  background: #fce4ec;
  color: #ad1457;
}

.category-tag.other {
  background: #f5f5f5;
  color: #616161;
}

.priority-tag.low {
  background: #e8f5e9;
  color: #2e7d32;
}

.priority-tag.medium {
  background: #fff3e0;
  color: #ef6c00;
}

.priority-tag.high {
  background: #ffebee;
  color: #c62828;
}

.priority-tag.urgent {
  background: #fce4ec;
  color: #ad1457;
}

.status-select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 0.8rem;
}

.status-select.open {
  background: #fff3e0;
  color: #ef6c00;
}

.status-select.in_progress {
  background: #e3f2fd;
  color: #1565c0;
}

.status-select.resolved {
  background: #e8f5e9;
  color: #2e7d32;
}



/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.btn-view,
.btn-respond {
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.2rem;
  transition: background-color 0.2s;
}

.btn-view:hover {
  background: #e3f2fd;
}

.btn-respond:hover {
  background: #e8f5e9;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.modal-header h2 {
  margin: 0;
  color: #333;
}

.modal-header button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 2rem;
}

.feedback-detail p {
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.message-section,
.response-section {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e0e0e0;
}

.message-content,
.response-content {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 0.5rem;
  line-height: 1.6;
  white-space: pre-wrap;
}

.response-date {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.5rem;
}

.feedback-summary {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.original-message {
  margin-top: 1rem;
}

.response-form label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.response-form textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  font-family: inherit;
  resize: vertical;
  box-sizing: border-box;
}

.response-form textarea:focus {
  outline: none;
  border-color: #4caf50;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e0e0e0;
}

.btn-cancel,
.btn-submit {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.btn-cancel:hover {
  background: #e0e0e0;
}

.btn-submit {
  background: #4caf50;
  color: white;
}

.btn-submit:hover {
  background: #45a049;
}

.no-feedback {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.loading,
.error {
  text-align: center;
  padding: 3rem;
  font-size: 1.1rem;
}

.error {
  color: #c62828;
}

/* Responsive Design */
@media (max-width: 768px) {
  .feedback-management {
    padding: 1rem;
  }

  .feedback-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .feedback-header h1 {
    font-size: 2rem;
  }

  .feedback-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
  }

  .feedback-filters {
    flex-direction: column;
    gap: 1rem;
  }

  .filter-group select {
    min-width: 100%;
  }

  .feedback-table-container {
    overflow-x: auto;
  }

  .feedback-table {
    min-width: 800px;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }

  .modal-header {
    padding: 1rem 1.5rem;
  }

  .modal-body {
    padding: 1.5rem;
  }

  .modal-actions {
    flex-direction: column-reverse;
  }

  .btn-cancel,
  .btn-submit {
    width: 100%;
  }
}
