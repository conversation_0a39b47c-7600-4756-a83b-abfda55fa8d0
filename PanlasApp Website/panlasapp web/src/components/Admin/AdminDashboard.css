/* ===== ADMIN DASHBOARD - CONSISTENT DESIGN ===== */
.admin-dashboard {
  max-width: 1200px;
  margin: var(--space-8) auto;
  padding: 0 var(--space-4);
  font-family: var(--font-family);
}

.admin-dashboard h1 {
  margin-bottom: var(--space-8);
  color: var(--text-primary);
  font-size: var(--font-size-3xl);
  font-weight: 700;
}

/* Modern Tab System */
.admin-tabs {
  display: flex;
  margin-bottom: var(--space-8);
  border-bottom: 2px solid var(--border-light);
  overflow-x: auto;
  background: var(--bg-primary);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  padding: 0 var(--space-4);
  box-shadow: var(--shadow-sm);
}

.tab-button {
  padding: var(--space-4) var(--space-6);
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  font-family: var(--font-family);
  position: relative;
}

.tab-button:hover {
  color: var(--text-primary);
  background-color: var(--gray-50);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  font-weight: 600;
  background-color: var(--primary-light);
}

/* Tab Content */
.tab-content {
  min-height: 400px;
  padding: var(--space-6);
  background: var(--bg-secondary);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

/* Modern Dashboard Cards */
.dashboard-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  padding: var(--space-8);
  margin-bottom: var(--space-8);
  border: 1px solid var(--border-light);
  transition: all 0.2s ease;
}

.dashboard-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Enhanced Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
  justify-items: center;
}

/* Enhanced Modern Stat Boxes */
.stat-box {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 280px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.stat-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(135deg, #20C5AF, #1ba896);
  border-radius: 16px 16px 0 0;
}

.stat-box:hover {
  transform: translateY(-6px);
  box-shadow: 0 8px 24px rgba(32, 197, 175, 0.15);
  border-color: #20C5AF;
}

.stat-box h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #6c757d;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
}

.stat-box h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 2px;
  background: #20C5AF;
  border-radius: 1px;
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  color: #20C5AF;
  margin: 0;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(32, 197, 175, 0.1);
}

/* Modern Admin Table */
.admin-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: var(--space-6);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.admin-table th,
.admin-table td {
  padding: var(--space-4) var(--space-6);
  text-align: left;
  border-bottom: 1px solid var(--border-light);
  font-family: var(--font-family);
}

.admin-table th {
  background: var(--gray-50);
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid var(--border-medium);
}

.admin-table td {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.admin-table tr:hover {
  background: var(--gray-50);
}

.admin-table tr:last-child td {
  border-bottom: none;
}

/* Modern Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
  align-items: center;
}

.action-buttons button {
  padding: var(--space-2) var(--space-3);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  font-family: var(--font-family);
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  box-shadow: var(--shadow-sm);
}

.action-buttons button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Admin Action Button Variants */
.btn-make-admin {
  background: var(--success-color);
  color: white;
}

.btn-make-admin:hover {
  background: var(--success-hover);
}

.btn-remove-admin {
  background: var(--warning-color);
  color: white;
}

.btn-remove-admin:hover {
  background: var(--warning-hover);
}

.btn-disable {
  background: var(--error-color);
  color: white;
}

.btn-disable:hover {
  background: var(--error-hover);
}

.btn-enable {
  background: var(--success-color);
  color: white;
}

.btn-enable:hover {
  background: var(--success-hover);
}

/* ===== MODERN FILTERS & SEARCH ===== */
.filters-section {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.filter-label {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  font-family: var(--font-family);
}

.filter-input,
.filter-select {
  padding: var(--space-3);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-family: var(--font-family);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.search-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px var(--primary-light);
}

.search-input::placeholder {
  color: var(--text-light);
}

/* Filter Buttons */
.filter-buttons {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
  margin-top: var(--space-4);
}

.filter-btn {
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: var(--font-family);
}

.filter-btn:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.filter-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Clear Filters Button */
.clear-filters-btn {
  padding: var(--space-2) var(--space-4);
  background: var(--gray-100);
  color: var(--text-secondary);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: var(--font-family);
}

.clear-filters-btn:hover {
  background: var(--gray-200);
  color: var(--text-primary);
}

/* ===== STATUS BADGES ===== */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: var(--font-family);
}

.status-badge.active {
  background: var(--success-light);
  color: var(--success-color);
}

.status-badge.disabled {
  background: var(--error-light);
  color: var(--error-color);
}

.status-badge.pending {
  background: var(--warning-light);
  color: var(--warning-color);
}

.status-badge.admin {
  background: var(--secondary-light);
  color: var(--secondary-color);
}

.status-badge.verified {
  background: var(--success-light);
  color: var(--success-color);
}

.status-badge.unverified {
  background: var(--gray-100);
  color: var(--gray-600);
}

/* ===== USER CARDS (Alternative to Table) ===== */
.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-4);
  margin-top: var(--space-6);
}

.user-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: all 0.2s ease;
}

.user-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.user-card-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--font-size-lg);
}

.user-info h3 {
  margin: 0;
  font-size: var(--font-size-base);
  color: var(--text-primary);
  font-weight: 600;
}

.user-info p {
  margin: var(--space-1) 0 0 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.user-card-body {
  margin-bottom: var(--space-4);
}

.user-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
}

.user-card-actions {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

/* ===== LOADING STATES ===== */
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-12);
  color: var(--text-secondary);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== ERROR STATES ===== */
.error-message {
  background: var(--error-light);
  color: var(--error-color);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  border: 1px solid var(--error-color);
  margin-bottom: var(--space-4);
  font-family: var(--font-family);
}

.empty-state {
  text-align: center;
  padding: var(--space-12);
  color: var(--text-secondary);
}

.empty-state h3 {
  margin-bottom: var(--space-2);
  color: var(--text-primary);
}

.empty-state p {
  margin-bottom: var(--space-4);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .admin-dashboard {
    margin: var(--space-4) auto;
    padding: 0 var(--space-3);
  }

  .admin-dashboard h1 {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--space-6);
  }

  .admin-tabs {
    padding: 0 var(--space-2);
    margin-bottom: var(--space-6);
  }

  .tab-button {
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-sm);
  }

  .tab-content {
    padding: var(--space-4);
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--space-4);
  }

  .stat-box {
    padding: var(--space-4);
  }

  .stat-number {
    font-size: var(--font-size-3xl);
  }

  .dashboard-card {
    padding: var(--space-6);
    margin-bottom: var(--space-6);
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .filter-buttons {
    gap: var(--space-2);
  }

  .users-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .user-card {
    padding: var(--space-4);
  }

  .action-buttons {
    gap: var(--space-1);
  }

  .action-buttons button {
    padding: var(--space-1) var(--space-2);
    font-size: 0.7rem;
  }

  /* Stack table on mobile */
  .admin-table {
    font-size: var(--font-size-xs);
  }

  .admin-table th,
  .admin-table td {
    padding: var(--space-2) var(--space-3);
  }
}

@media (max-width: 480px) {
  .admin-dashboard {
    padding: 0 var(--space-2);
  }

  .tab-button {
    padding: var(--space-2) var(--space-3);
    font-size: 0.8rem;
  }

  .stat-box {
    padding: var(--space-3);
  }

  .stat-number {
    font-size: var(--font-size-2xl);
  }

  .dashboard-card {
    padding: var(--space-4);
  }

  .user-card {
    padding: var(--space-3);
  }

  .user-card-header {
    gap: var(--space-2);
  }

  .user-avatar {
    width: 40px;
    height: 40px;
    font-size: var(--font-size-base);
  }
}

/* ===== MODERN SCROLLBARS ===== */
.admin-dashboard ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.admin-dashboard ::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--radius-full);
}

.admin-dashboard ::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: var(--radius-full);
}

.admin-dashboard ::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

/* ===== ACCESSIBILITY ===== */
.admin-dashboard *:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.admin-dashboard button:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {
  .admin-dashboard {
    box-shadow: none;
    margin: 0;
    padding: 0;
  }

  .tab-content {
    box-shadow: none;
    border: 1px solid var(--border-medium);
  }

  .dashboard-card {
    box-shadow: none;
    border: 1px solid var(--border-medium);
    break-inside: avoid;
  }

  .action-buttons {
    display: none;
  }
}

.btn-disable:hover {
  background-color: #d32f2f;
  transform: translateY(-1px);
}

.btn-enable {
  background-color: #4CAF50;
  color: white;
}

.btn-enable:hover {
  background-color: #45a049;
  transform: translateY(-1px);
}

/* Status badges */
.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.active {
  background-color: #e8f5e8;
  color: black;
  border: 1px solid black;
}

.status-badge.disabled {
  background-color: #ffebee;
  color: black;
  border: 1px solid black;
}

.status-badge.verified {
  background-color: #e3f2fd;
  color: black;
  border: 1px solid black;
}

.status-badge.unverified {
  background-color: #fff3e0;
  color: black;
  border: 1px solid black;
}

/* Disabled user row styling */
.disabled-user {
  opacity: 0.6;
  background-color: #fafafa;
}

.disabled-user td {
  color: #666;
}

/* Disabled button styling */
.action-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Enhanced Chart Section */
.chart-section {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
  height: 400px;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 16px;
  padding: 2rem;
  border: 2px solid #e9ecef;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.chart-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #20C5AF, #1ba896);
  border-radius: 16px 16px 0 0;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.error {
  text-align: center;
  padding: 2rem;
  color: #f44336;
}

/* Enhanced Responsive adjustments */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    max-width: 400px;
    margin: 0 auto 2rem auto;
  }

  .stat-box {
    max-width: 100%;
    padding: 1.5rem;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .chart-section {
    padding: 1.5rem;
    height: 350px;
  }
}

  .admin-table {
    font-size: 0.9rem;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.5rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.3rem;
  }

  .action-buttons button {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
  }
}

/* User Statistics Grid */
.user-stats-grid {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.user-stats-grid .stat-item {
  transition: transform 0.2s ease;
}

/* Make all numbers in user stats grid black */
.user-stats-grid .stat-number,
.user-stats-grid .stat-item h2,
.user-stats-grid .stat-item h3,
.user-stats-grid .stat-item p,
.user-stats-grid .stat-item span {
  color: black !important;
}

.user-stats-grid .stat-item:hover {
  transform: translateY(-2px);
}

/* User Filters */
.user-filters {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.user-filters label {
  color: #495057;
  font-weight: 600;
}

.user-filters input,
.user-filters select {
  transition: all 0.2s ease;
  background-color: white;
}

.user-filters input:focus,
.user-filters select:focus {
  outline: none;
  border-color: #20C5AF;
  box-shadow: 0 0 0 2px rgba(32, 197, 175, 0.2);
}

.user-filters input:hover,
.user-filters select:hover {
  border-color: #20C5AF;
}

/* Filter Results Info */
.filter-results-info {
  color: #6c757d;
  font-style: italic;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

/* Enhanced Table Styling for Signup Statistics */
.dashboard-card .admin-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 2px solid #e9ecef;
  margin-top: 2rem;
}

.dashboard-card .admin-table thead th {
  background: linear-gradient(135deg, #20C5AF, #1ba896);
  color: white;
  font-weight: 700;
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 1px;
  padding: 1.25rem 1.5rem;
  text-align: center;
}

.dashboard-card .admin-table tbody td {
  padding: 1rem 1.5rem;
  text-align: center;
  font-size: 1rem;
  font-weight: 500;
  color: #2c3e50;
  border-bottom: 1px solid #f1f3f4;
}

.dashboard-card .admin-table tbody tr {
  transition: all 0.3s ease;
}

.dashboard-card .admin-table tbody tr:hover {
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(32, 197, 175, 0.1);
}

.dashboard-card .admin-table tbody tr:last-child td {
  border-bottom: none;
}

/* Table section header */
.dashboard-card h3 {
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 700;
  margin: 2rem 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #20C5AF;
  display: inline-block;
}

/* No Results Message */
.no-results {
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  color: #6c757d;
  font-style: italic;
}

@media (max-width: 768px) {
  .user-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 10px;
    padding: 10px;
  }

  .user-filters {
    grid-template-columns: 1fr;
    gap: 10px;
    padding: 10px;
  }

  .user-filters input,
  .user-filters select {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-box {
    padding: 1.25rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .chart-section {
    padding: 1rem;
    height: 300px;
  }

  .admin-tabs {
    flex-wrap: wrap;
  }

  .tab-button {
    flex: 1 0 auto;
    text-align: center;
    padding: 0.5rem 1rem;
  }

  .user-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .admin-table {
    font-size: 0.8rem;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.4rem;
  }
}

/* Activity Logs Specific Styles */
.activity-stats-grid {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.activity-stats-grid .stat-item {
  transition: transform 0.2s ease;
}

.activity-stats-grid .stat-item:hover {
  transform: translateY(-2px);
}

.activity-filters {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.activity-filters input,
.activity-filters select {
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.activity-filters input:focus,
.activity-filters select:focus {
  outline: none;
  border-color: #20C5AF;
  box-shadow: 0 0 0 2px rgba(32, 197, 175, 0.2);
}

.activity-filters input:hover,
.activity-filters select:hover {
  border-color: #20C5AF;
}

/* Activity Table Enhancements */
.admin-table tbody tr:hover {
  background-color: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Action Badge Styles */
.action-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Details Expansion */
details summary {
  transition: color 0.2s ease;
}

details summary:hover {
  color: #20C5AF !important;
}

details[open] summary {
  margin-bottom: 5px;
}

/* Pagination Buttons */
.pagination-button {
  transition: all 0.2s ease;
}

.pagination-button:hover:not(:disabled) {
  background-color: #20C5AF !important;
  color: white !important;
  border-color: #20C5AF !important;
}

.pagination-button:disabled {
  opacity: 0.6;
}

/* Filter Results Info */
.filter-results-info {
  color: #6c757d;
  font-style: italic;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
}

/* Responsive Design for Activity Logs */
@media (max-width: 768px) {
  .activity-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 10px;
    padding: 10px;
  }

  .activity-filters {
    grid-template-columns: 1fr;
    gap: 10px;
    padding: 10px;
  }

  .activity-filters input,
  .activity-filters select {
    font-size: 16px;
  }

  .admin-table {
    font-size: 0.8rem;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.4rem;
  }

  /* Stack table cells on mobile */
  .admin-table thead {
    display: none;
  }

  .admin-table tbody tr {
    display: block;
    border: 1px solid #ddd;
    margin-bottom: 10px;
    border-radius: 8px;
    padding: 10px;
  }

  .admin-table tbody td {
    display: block;
    text-align: left;
    border: none;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
  }

  .admin-table tbody td:last-child {
    border-bottom: none;
  }

  .admin-table tbody td:before {
    content: attr(data-label) ": ";
    font-weight: bold;
    color: #333;
  }
}

/* ===== CUSTOM INPUT CONTAINER FOR "OTHER" OPTIONS ===== */
.custom-input-container {
  display: flex;
  gap: var(--space-2);
  margin-top: var(--space-3);
  padding: var(--space-3);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  align-items: center;
}

.custom-input {
  flex: 1;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-family: var(--font-family);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color 0.2s ease;
}

.custom-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-light);
}

.custom-input::placeholder {
  color: var(--text-tertiary);
  font-style: italic;
}

.add-custom-btn {
  padding: var(--space-2) var(--space-4);
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: var(--font-family);
  white-space: nowrap;
}

.add-custom-btn:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

.add-custom-btn:active {
  transform: translateY(0);
}

/* Responsive custom input */
@media (max-width: 768px) {
  .custom-input-container {
    flex-direction: column;
    gap: var(--space-2);
    align-items: stretch;
  }

  .add-custom-btn {
    align-self: flex-start;
  }
}

/* ===== REFRESH BUTTON ANIMATIONS ===== */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Refresh button styles */
.btn-refresh {
  transition: all 0.3s ease;
}

.btn-refresh:hover:not(:disabled) {
  background: #1ba896 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(32, 197, 175, 0.3);
}
