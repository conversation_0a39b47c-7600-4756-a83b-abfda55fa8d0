import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';

function SignupChart({ monthlyStats }) {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    if (!monthlyStats || !chartRef.current) return;

    // Destroy previous chart if it exists
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    const ctx = chartRef.current.getContext('2d');
    
    // Extract labels (months) and data (signup counts)
    const labels = monthlyStats.map(stat => stat.month);
    const data = monthlyStats.map(stat => stat.count);

    // Create new chart
    chartInstance.current = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [{
          label: 'New User Signups',
          data: data,
          backgroundColor: 'rgba(76, 175, 80, 0.6)', // Match your app's primary color
          borderColor: 'rgba(76, 175, 80, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              precision: 0 // Only show whole numbers
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Monthly User Signups',
            font: {
              size: 16
            }
          },
          legend: {
            position: 'bottom'
          }
        }
      }
    });

    // Cleanup function
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [monthlyStats]);

  return (
    <div style={{ height: '100%', width: '100%' }}>
      <canvas ref={chartRef}></canvas>
    </div>
  );
}

export default SignupChart;
