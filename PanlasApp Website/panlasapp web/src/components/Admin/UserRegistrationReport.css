/* User Registration Report Container */
.user-registration-report {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8f9fa;
  min-height: 100vh;
  text-align: center;
}

.user-registration-report h2 {
  color: #2c3e50;
  font-size: 2.5rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

/* Report Controls */
.report-controls {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-end;
}

.date-range {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.date-input {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.date-input label {
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
}

.date-input input {
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
  min-width: 150px;
}

.date-input input:focus {
  outline: none;
  border-color: #20C5AF;
}

.generate-btn {
  padding: 0.75rem 1.5rem;
  background: #20C5AF;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  align-self: flex-end;
}

.generate-btn:hover {
  background: #1ba896;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(32, 197, 175, 0.3);
}

.generate-btn:disabled {
  background: #cccccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.export-btn {
  padding: 0.75rem 1.5rem;
  background: #20C5AF;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.export-btn:hover {
  background: #1ba896;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(32, 197, 175, 0.3);
}

/* Error Message */
.report-error {
  color: #dc3545;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  text-align: center;
  font-weight: 500;
}

/* Report Results */
.report-results {
  margin-top: 2rem;
}

.report-summary {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  text-align: left;
}

.report-summary h3 {
  color: #2c3e50;
  font-size: 1.8rem;
  margin-top: 0;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.report-summary p {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0.75rem 0;
}

.report-summary strong {
  color: #2c3e50;
}

/* Report Table */
.report-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.report-table th,
.report-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #ecf0f1;
}

.report-table th {
  background: #20C5AF;
  color: white;
  font-weight: 600;
  font-size: 1rem;
}

.report-table td {
  color: #2c3e50;
  font-size: 0.95rem;
}

.report-table tr:hover {
  background: #f8f9fa;
}

.report-table tr:last-child td {
  border-bottom: none;
}

.no-data {
  text-align: center;
  padding: 3rem;
  color: #7f8c8d;
  font-style: italic;
  font-size: 1.1rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-registration-report {
    padding: 1rem;
  }

  .user-registration-report h2 {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .report-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .date-range {
    flex-direction: column;
    gap: 1rem;
  }

  .date-input input {
    min-width: auto;
  }

  .generate-btn,
  .export-btn {
    align-self: stretch;
    text-align: center;
  }

  .report-summary {
    padding: 1.5rem;
    text-align: center;
  }

  .report-summary h3 {
    font-size: 1.5rem;
    justify-content: center;
  }

  .report-table {
    font-size: 0.85rem;
  }

  .report-table th,
  .report-table td {
    padding: 0.75rem 0.5rem;
  }

  .no-data {
    padding: 2rem 1rem;
  }
}
