import React, { useEffect, useState } from "react";
import Layout from "../Layout/Layout";
import axios from "axios";
import { FaClock } from "react-icons/fa";
const RECENTLY_VIEWED_KEY = "recentlyViewedMeals";

function getDietaryBadges(meal) {
  const BADGE_MAP = [
    { key: 'Vegan', label: 'Vegan', color: '#4CAF50', icon: '🌱' },
    { key: 'Vegetarian', label: 'Vegetarian', color: '#8BC34A', icon: '🥬' },
    { key: 'Flexitarian', label: 'Flexitarian', color: '#A3C9A8', icon: '🤝' },
    { key: 'Dairy-Free', label: 'Dairy-Free', color: '#00BCD4', icon: '🥛🚫' },
    { key: 'Egg-Free', label: 'Egg-Free', color: '#FFEB3B', icon: '🥚🚫' },
    { key: 'Gluten-Free', label: 'Gluten-Free', color: '#FF9800', icon: '🌾' },
    { key: 'Soy-Free', label: 'Soy-Free', color: '#9E9E9E', icon: '🌱🚫' },
    { key: 'Nut-Free', label: 'Nut-Free', color: '#795548', icon: '🥜🚫' },
    { key: 'Low-Carb', label: 'Low-Carb', color: '#9C27B0', icon: '🥩' },
    { key: 'Low-Sugar', label: 'Low-Sugar', color: '#607D8B', icon: '🍬⬇️' },
    { key: 'Sugar-Free', label: 'Sugar-Free', color: '#607D8B', icon: '🍬🚫' },
    { key: 'Low-Fat', label: 'Low-Fat', color: '#03A9F4', icon: '🥗' },
    { key: 'Low-Sodium', label: 'Low-Sodium', color: '#B0BEC5', icon: '🧂⬇️' },
    { key: 'Organic', label: 'Organic', color: '#388E3C', icon: '🍃' },
    { key: 'Halal', label: 'Halal', color: '#2196F3', icon: '☪️' },
    { key: 'High-Protein', label: 'High-Protein', color: '#E91E63', icon: '💪' },
    { key: 'Pescatarian', label: 'Pescatarian', color: '#00B8D4', icon: '🐟' },
    { key: 'Keto', label: 'Keto', color: '#FFB300', icon: '🥓' },
    { key: 'Plant-Based', label: 'Plant-Based', color: '#43A047', icon: '🌿' },
    { key: 'Kosher', label: 'Kosher', color: '#3F51B5', icon: '✡️' },
    { key: 'Climatarian', label: 'Climatarian', color: '#689F38', icon: '🌎' },
    { key: 'Raw Food', label: 'Raw Food', color: '#AED581', icon: '🥗' },
    { key: 'Mediterranean', label: 'Mediterranean', color: '#00ACC1', icon: '🌊' },
    { key: 'Paleo', label: 'Paleo', color: '#A1887F', icon: '🍖' },
    { key: 'Kangatarian', label: 'Kangatarian', color: '#D84315', icon: '🦘' },
    { key: 'Pollotarian', label: 'Pollotarian', color: '#FBC02D', icon: '🍗' },
  ];
  const tags = meal.dietaryTags || [];
  return BADGE_MAP.filter(badge =>
    tags.some(tag => tag.toLowerCase() === badge.key.toLowerCase())
  );
}
// Convert price range to peso signs
const getPesoSigns = (priceRange) => {
  switch (priceRange) {
    case "Low": return "₱";
    case "Mid": return "₱₱";
    case "High": return "₱₱₱";
    default: return "₱";
  }
};

const History = () => {
  const [recentMeals, setRecentMeals] = useState([]);
  const [mealsFromSavedPlans, setMealsFromSavedPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshingRecent, setRefreshingRecent] = useState(false);
  const [refreshingSaved, setRefreshingSaved] = useState(false);
  const [error, setError] = useState(null);
  const token = localStorage.getItem("token");

  // Function to load recently viewed meals
  const loadRecentlyViewedMeals = async (showRefreshingState = false) => {
    if (!token) {
      console.log('No token available for loading recently viewed meals');
      return;
    }

    if (showRefreshingState) {
      setRefreshingRecent(true);
    }

    try {
      console.log('=== LOADING RECENTLY VIEWED MEALS (WEBSITE) ===');
      console.log('Token:', token ? 'exists' : 'missing');

      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await axios.get(`${API_BASE_URL}/users/recently-viewed-meals`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      console.log('Website API response:', JSON.stringify(response.data, null, 2));

      const meals = response.data.recentlyViewedMeals || [];
      console.log('Setting recent meals count:', meals.length);
      console.log('Recent meals:', JSON.stringify(meals.map(m => ({ name: m.name, id: m.id || m._id })), null, 2));

      setRecentMeals(meals);
      setError(null); // Clear any previous errors
    } catch (error) {
      console.error('Error loading recently viewed meals:', error);
      console.error('Error details:', JSON.stringify(error.response?.data || error, null, 2));
      setRecentMeals([]);

      if (showRefreshingState) {
        let errorMessage = 'Failed to refresh recently viewed meals. ';
        if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
          errorMessage += 'Please check your internet connection.';
        } else if (error.response?.status === 401) {
          errorMessage += 'Please log in again.';
        } else if (error.response?.status === 500) {
          errorMessage += 'Server error. Please try again later.';
        } else {
          errorMessage += 'Please try again.';
        }
        setError(errorMessage);
      }
    } finally {
      if (showRefreshingState) {
        setRefreshingRecent(false);
      }
    }
  };

  // Function to load meals from saved plans
  const loadMealsFromSavedPlans = async (showRefreshingState = false) => {
    if (!token) return;

    if (showRefreshingState) {
      setRefreshingSaved(true);
    }

    try {
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await axios.get(`${API_BASE_URL}/users/meals-from-saved-plans`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setMealsFromSavedPlans(response.data.mealsFromSavedPlans || []);
      setError(null); // Clear any previous errors
    } catch (error) {
      console.error('Error loading meals from saved plans:', error);
      setMealsFromSavedPlans([]);

      if (showRefreshingState) {
        let errorMessage = 'Failed to refresh meals from saved plans. ';
        if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
          errorMessage += 'Please check your internet connection.';
        } else if (error.response?.status === 401) {
          errorMessage += 'Please log in again.';
        } else if (error.response?.status === 500) {
          errorMessage += 'Server error. Please try again later.';
        } else {
          errorMessage += 'Please try again.';
        }
        setError(errorMessage);
      }
    } finally {
      if (showRefreshingState) {
        setRefreshingSaved(false);
      }
    }
  };

  // Function to load all history data
  const loadHistoryData = async () => {
    setLoading(true);
    await Promise.all([
      loadRecentlyViewedMeals(),
      loadMealsFromSavedPlans()
    ]);
    setLoading(false);
  };

  // Load data on mount
  useEffect(() => {
    loadHistoryData();
  }, [token]);

  // Auto-clear error after 5 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        setError(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  // Add event listener for when user returns to this page
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible, refresh the data
        loadHistoryData();
      }
    };

    const handleFocus = () => {
      // Window gained focus, refresh the data
      loadHistoryData();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [token]);

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format meal type for display
  const formatMealType = (mealType) => {
    return mealType.charAt(0).toUpperCase() + mealType.slice(1);
  };

  return (
    <Layout>
      <div className="main-content">
        {/* Error Display */}
        {error && (
          <div style={{
            backgroundColor: '#ffebee',
            color: '#c62828',
            padding: '12px 16px',
            borderRadius: '4px',
            marginBottom: '20px',
            border: '1px solid #ffcdd2',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <span>{error}</span>
            <button
              onClick={() => setError(null)}
              style={{
                background: 'none',
                border: 'none',
                color: '#c62828',
                cursor: 'pointer',
                fontSize: '18px',
                padding: '0 4px'
              }}
            >
              ×
            </button>
          </div>
        )}

        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h1>Family Meal History</h1>
          <button
            onClick={loadHistoryData}
            disabled={loading}
            style={{
              padding: '10px 20px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: loading ? 'not-allowed' : 'pointer',
              opacity: loading ? 0.6 : 1
            }}
          >
            {loading ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>

          {/* Meals from Saved Plans Section */}
          <div className="history-section">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
              <h2>Recently Added to Family Meal Plans</h2>
              <button
                onClick={() => loadMealsFromSavedPlans(true)}
                disabled={refreshingSaved}
                style={{
                  padding: '10px 20px',
                  backgroundColor: refreshingSaved ? '#ccc' : '#1890ff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '5px',
                  cursor: refreshingSaved ? 'not-allowed' : 'pointer',
                  opacity: refreshingSaved ? 0.6 : 1,
                  fontSize: '12px',
                  transition: 'all 0.3s ease'
                }}
              >
                {refreshingSaved ? 'Refreshing...' : 'Refresh'}
              </button>
            </div>
            {mealsFromSavedPlans.length === 0 ? (
              <p>No meals from saved plans yet.</p>
            ) : (
              <div className="food-grid">
                {mealsFromSavedPlans.map((meal, index) => (
<div key={`${meal.name}-${index}`} className="food-card meal-plan-card">
  <div className="food-card-image">
    <img src={meal.image} alt={meal.name} />
  </div>
  <div className="food-card-content">
    {/* 1. Meal Name */}
    <h3 className="food-card-title">{meal.name}</h3>
    {/* 2. Meta row: Calories, Prep Time, Rating */}
    <div className="food-card-meta">
      {meal.calories && (
        <div className="meta-item calories-tag">
          <span>{meal.calories} cal</span>
        </div>
      )}
      {meal.prepTime && (
        <div className="meta-item prep-time-tag">
          <FaClock /> <span>{meal.prepTime} min</span>
        </div>
      )}
      <div className="meta-item rating">
        <span>{meal.rating} &#9733;</span>
      </div>
    </div>
    {/* 3. Dietary Tags */}
    {getDietaryBadges(meal).length > 0 && (
      <div className="food-card-tags">
        {getDietaryBadges(meal).map((badge, idx) => (
          <span
            key={idx}
            className="dietary-tag"
            style={{
              background: badge.color,
              color: '#fff',
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.25em',
            }}
            title={badge.label}
          >
            <span className="dietary-tag-icon">{badge.icon}</span>
            <span className="dietary-tag-label">{badge.label}</span>
          </span>
        ))}
      </div>
    )}
    {/* 4. Description (optional, if you want) */}
    {meal.description && (
      <div className="food-card-description">
        <span>
          {meal.description.length > 80
            ? meal.description.slice(0, 80) + "..."
            : meal.description}
        </span>
      </div>
    )}
    {/* 5. Meal Plan Info */}
    <div className="meal-plan-info">
      <div className="meal-plan-details">
        <span className={`meal-type-badge meal-type-${meal.addedToMealType?.toLowerCase()}`}>
          {formatMealType(meal.addedToMealType)}
        </span>
        <span className="added-date">
          Added {formatDate(meal.addedAt)}
        </span>
      </div>
      <div className="planned-for">
        Planned for: {new Date(meal.addedToDate).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric'
        })}
      </div>
      {meal.planName && (
        <div className="plan-name" style={{
          fontSize: '0.85em',
          color: '#666',
          fontStyle: 'italic',
          marginTop: '4px'
        }}>
          From: {meal.planName}
        </div>
      )}
    </div>
  </div>
</div>
                ))}
              </div>
            )}
          </div>

          {/* Recently Viewed Meals Section */}
          <div className="history-section">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
              <h2>Recently Viewed Meals</h2>
              <button
                onClick={() => loadRecentlyViewedMeals(true)}
                disabled={refreshingRecent}
                style={{
                  padding: '10px 20px',
                  backgroundColor: refreshingRecent ? '#ccc' : '#1890ff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '5px',
                  cursor: refreshingRecent ? 'not-allowed' : 'pointer',
                  opacity: refreshingRecent ? 0.6 : 1,
                  fontSize: '12px',
                  transition: 'all 0.3s ease'
                }}
              >
                {refreshingRecent ? 'Refreshing...' : 'Refresh'}
              </button>
            </div>
            {loading ? (
              <p>Loading recently viewed meals...</p>
            ) : recentMeals.length === 0 ? (
              <p>No recently viewed meals yet. Try viewing some meals first!</p>
            ) : (
              <div className="food-grid">
                {recentMeals.map((meal, index) => (
                  <div key={`${meal.name}-${index}`} className="food-card meal-plan-card">
                    <div className="food-card-image">
                      <img src={meal.image} alt={meal.name} />
                    </div>
                    <div className="food-card-content">
                      {/* 1. Meal Name */}
                      <h3 className="food-card-title">{meal.name}</h3>
                      {/* 2. Meta row: Calories, Prep Time, Rating */}
                      <div className="food-card-meta">
                        {meal.calories && (
                          <div className="meta-item calories-tag">
                            <span>{meal.calories} cal</span>
                          </div>
                        )}
                        {meal.prepTime && (
                          <div className="meta-item prep-time-tag">
                            <FaClock /> <span>{meal.prepTime} min</span>
                          </div>
                        )}
                        <div className="meta-item rating">
                          <span>{meal.rating} &#9733;</span>
                        </div>
                      </div>
                      {/* 3. Dietary Tags */}
                      {getDietaryBadges(meal).length > 0 && (
                        <div className="food-card-tags">
                          {getDietaryBadges(meal).map((badge, idx) => (
                            <span
                              key={idx}
                              className="dietary-tag"
                              style={{
                                background: badge.color,
                                color: '#fff',
                                display: 'inline-flex',
                                alignItems: 'center',
                                gap: '0.25em',
                              }}
                              title={badge.label}
                            >
                              <span className="dietary-tag-icon">{badge.icon}</span>
                              <span className="dietary-tag-label">{badge.label}</span>
                            </span>
                          ))}
                        </div>
                      )}
                      {/* 4. Description (optional, if you want) */}
                      {meal.description && (
                        <div className="food-card-description">
                          <span>
                            {meal.description.length > 80
                              ? meal.description.slice(0, 80) + "..."
                              : meal.description}
                          </span>
                        </div>
                      )}
                      {/* 5. Recently Viewed Info */}
                      <div className="meal-plan-info">
                        <div className="meal-plan-details">
                          <span className="meal-type-badge meal-type-viewed">
                            Recently Viewed
                          </span>
                          {meal.category && (
                            <span className="added-date">
                              Category: {meal.category}
                            </span>
                          )}
                        </div>
                        {meal.priceRange && (
                          <div className="planned-for">
                            Price Range: {getPesoSigns(meal.priceRange)}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
      </div>
    </Layout>
  );
};

export default History;