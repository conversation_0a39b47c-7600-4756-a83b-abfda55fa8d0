import React, { useEffect } from "react";
import { BrowserRouter as Router, Routes, Route, useLocation } from "react-router-dom";
import analyticsService from "./services/analyticsService";
import Landing from "./components/1stPage/Landing";
import Home from "./components/Home/Home";
import Chat from "./components/Chat/Chat";
import History from "./components/History/History";
import Family from "./components/Family/Family";
import Helpcenter from "./components/HelpCenter/Helpcenter";
import MealPlan from "./components/MealPlan/Mealplan";
import Login from "./components/Login/Login";
import SignUp from "./components/SignUp/SignUp";
import ForgotPassword from "./components/ForgotPassword/ForgotPassword";
import ResetPassword from "./components/ResetPassword/ResetPassword";
import OTPVerification from "./components/OTPVerification/OTPVerification";
import PrivateRoute from "./components/Protected-Route/PrivateRoute";
import AdminRoute from "./components/Protected-Route/AdminRoute";
import UserProfile from "./components/UserProfile/UserProfile";
import DietaryPreferences from "./components/DietaryPreferences/DietaryPreferences";
import AdminDashboard from "./components/Admin/AdminDashboard";
import FeedbackManagement from "./components/Admin/FeedbackManagement";
import MealManagement from "./components/Admin/MealManagement";
import Favorites from "./components/Favorites/Favorites";
import TermsEnforcement from "./components/TermsEnforcement/TermsEnforcement";
import { AdminViewProvider } from "./context/AdminViewContext";
import { FavoritesProvider } from "./components/Favorites/FavoritesContext";

// Analytics tracking component
const AnalyticsTracker = () => {
  const location = useLocation();

  useEffect(() => {
    // Track page view when location changes
    analyticsService.trackPageView(location.pathname);
  }, [location]);

  return null;
};

const App = () => {
  return (
    <AdminViewProvider>
      <FavoritesProvider>
        <Router>
          <AnalyticsTracker />
          <TermsEnforcement>
            <Routes>
          <Route path="/" element={<Landing/>}/>
          <Route path="/login" element={<Login/>}/>
          <Route path="/signup" element={<SignUp/>}/>
          <Route path="/forgot-password" element={<ForgotPassword/>}/>
          <Route path="/reset-password" element={<ResetPassword/>}/>
          <Route path="/otp-verification" element={<OTPVerification/>}/>
          <Route path="/home" element={
            <PrivateRoute>
              <Home />
            </PrivateRoute>}/>
          <Route path="/profile" element={
            <PrivateRoute>
              <UserProfile />
            </PrivateRoute>
          } />
          <Route path="/dietary-preferences" element={
            <PrivateRoute>
              <DietaryPreferences />
            </PrivateRoute>
          } />
          <Route path="/admin" element={
            <AdminRoute>
              <AdminDashboard />
            </AdminRoute>
          } />
          <Route path="/feedback-management" element={
            <AdminRoute>
              <FeedbackManagement />
            </AdminRoute>
          } />
          <Route path="/meal-management" element={
            <AdminRoute>
              <MealManagement />
            </AdminRoute>
          } />
          <Route path="/chat" element={<Chat/>}/>
          <Route path="/favorites" element={<Favorites/>}/>
          <Route path="/history" element={<History/>}/>
          <Route path="/family" element={<Family/>}/>
          <Route path="/help-center" element={<Helpcenter/>}/>
          <Route path="/meal-plan" element={<MealPlan/>}/>
            </Routes>
          </TermsEnforcement>
        </Router>
      </FavoritesProvider>
    </AdminViewProvider>
  );
}

export default App;
