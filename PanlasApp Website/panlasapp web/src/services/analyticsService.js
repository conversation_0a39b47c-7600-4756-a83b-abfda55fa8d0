import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const analyticsAPI = axios.create({
  baseURL: `${API_BASE_URL}/analytics`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
analyticsAPI.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  
  // Add session ID for tracking
  let sessionId = sessionStorage.getItem('sessionId');
  if (!sessionId) {
    sessionId = generateSessionId();
    sessionStorage.setItem('sessionId', sessionId);
  }
  config.headers['x-session-id'] = sessionId;
  
  return config;
});

// Generate unique session ID
const generateSessionId = () => {
  return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

// Detect device information
const getDeviceInfo = () => {
  const userAgent = navigator.userAgent;
  const platform = /Mobile|Android|iPhone|iPad/.test(userAgent) ? 'mobile' : 
                  /Tablet|iPad/.test(userAgent) ? 'tablet' : 'desktop';
  
  return {
    userAgent,
    platform,
    isMobile: platform === 'mobile',
    screenResolution: `${screen.width}x${screen.height}`,
    viewport: `${window.innerWidth}x${window.innerHeight}`,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  };
};

// Analytics Service
class AnalyticsService {
  constructor() {
    this.sessionId = sessionStorage.getItem('sessionId') || generateSessionId();
    this.deviceInfo = getDeviceInfo();
    this.pageStartTime = Date.now();
    this.isEnabled = false;

    // Check if user is authenticated before enabling analytics
    this.checkAuthAndInitialize();

    // Track page unload
    window.addEventListener('beforeunload', () => {
      if (this.isEnabled) {
        this.trackEvent('page_unload', {
          timeOnPage: Date.now() - this.pageStartTime,
          exitPage: window.location.pathname
        });
      }
    });
  }

  // Check authentication and initialize analytics
  checkAuthAndInitialize() {
    const token = localStorage.getItem('token');
    if (token) {
      this.isEnabled = true;
      // Track page views automatically only for authenticated users
      this.trackPageView();
    }
  }

  // Enable analytics when user logs in
  enable() {
    this.isEnabled = true;
    this.trackPageView();
  }

  // Disable analytics when user logs out
  disable() {
    this.isEnabled = false;
  }

  // Track custom events
  async trackEvent(event, eventData = {}) {
    // Only track if analytics is enabled (user is authenticated)
    if (!this.isEnabled) {
      return;
    }

    try {
      const payload = {
        event,
        eventData: {
          ...eventData,
          timestamp: new Date().toISOString(),
          page: window.location.pathname,
          referrer: document.referrer
        },
        deviceInfo: this.deviceInfo
      };

      await analyticsAPI.post('/track', payload);
    } catch (error) {
      // If authentication fails, disable analytics
      if (error.response?.status === 401) {
        console.warn('Analytics disabled due to authentication failure');
        this.disable();
      } else {
        console.error('Analytics tracking error:', error);
      }
    }
  }

  // Track page views
  trackPageView(page = window.location.pathname) {
    if (!this.isEnabled) {
      return;
    }

    this.pageStartTime = Date.now();
    this.trackEvent('page_view', {
      page,
      title: document.title,
      referrer: document.referrer
    });
  }

  // Track user interactions
  trackClick(element, data = {}) {
    this.trackEvent('click', {
      element,
      ...data
    });
  }

  trackSearch(query, filters = {}) {
    this.trackEvent('search', {
      query,
      filters
    });
  }

  trackMealView(mealId, mealName) {
    this.trackEvent('meal_view', {
      mealId,
      mealName
    });
  }

  trackMealPlanAction(action, data = {}) {
    this.trackEvent(`meal_plan_${action}`, data);
  }

  trackError(error, context = {}) {
    this.trackEvent('error', {
      error: error.message || error,
      stack: error.stack,
      context
    });
  }

  // Admin Analytics API calls
  async getDashboardAnalytics(timeRange = '7d', userType = 'all') {
    try {
      const response = await analyticsAPI.get('/dashboard', {
        params: { timeRange, userType }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard analytics:', error);
      throw error;
    }
  }

  async getUserActivityAnalytics(timeRange = '30d', status = 'all') {
    try {
      const response = await analyticsAPI.get('/users', {
        params: { timeRange, status }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching user activity analytics:', error);
      throw error;
    }
  }

  async getPlatformAnalytics(timeRange = '30d') {
    try {
      const response = await analyticsAPI.get('/platforms', {
        params: { timeRange }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching platform analytics:', error);
      throw error;
    }
  }
}

// Create singleton instance
const analyticsService = new AnalyticsService();

// Export both the service and individual methods for convenience
export default analyticsService;

export const {
  trackEvent,
  trackPageView,
  trackClick,
  trackSearch,
  trackMealView,
  trackMealPlanAction,
  trackError,
  getDashboardAnalytics,
  getUserActivityAnalytics,
  getPlatformAnalytics
} = analyticsService;
