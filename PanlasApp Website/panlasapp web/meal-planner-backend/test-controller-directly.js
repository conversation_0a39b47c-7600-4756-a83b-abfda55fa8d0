require('dotenv').config();
const mongoose = require('mongoose');
const mealPlanController = require('./controllers/mealPlanController');

async function testControllerDirectly() {
  try {
    console.log('🔍 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Mock request and response objects
    const req = {
      params: { date: '2025-08-14' },
      body: { isLocked: true },
      user: { id: '685883ea3cc2df1d683b8714' }
    };

    const res = {
      statusCode: 200, // Default to 200
      status: function(code) {
        this.statusCode = code;
        return this;
      },
      json: function(data) {
        this.responseData = data;
        return this;
      }
    };

    console.log('\n🧪 Testing toggleLockMealPlan controller directly...');
    console.log('Request params:', req.params);
    console.log('Request body:', req.body);
    console.log('User ID:', req.user.id);

    // Call the controller function directly
    await mealPlanController.toggleLockMealPlan(req, res);

    console.log('\n✅ Controller response:');
    console.log('Status Code:', res.statusCode);
    console.log('Response Data:', res.responseData);

    if (res.statusCode === 200) {
      console.log('\n🎉 SUCCESS! The fix is working correctly.');
    } else {
      console.log('\n❌ FAILED! Status code:', res.statusCode);
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

testControllerDirectly();
