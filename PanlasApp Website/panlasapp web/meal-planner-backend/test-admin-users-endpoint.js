require('dotenv').config();
const axios = require('axios');

async function testAdminUsersEndpoint() {
  try {
    console.log('🔍 Testing admin users endpoint...');
    
    // You'll need to replace this with a valid admin token
    // You can get this from the browser's localStorage when logged in as admin
    const adminToken = 'YOUR_ADMIN_TOKEN_HERE'; // Replace with actual token
    
    const API_BASE_URL = 'http://localhost:5000/api';
    
    console.log('🔗 Making request to:', `${API_BASE_URL}/admin/users`);
    
    const config = {
      headers: {
        'x-auth-token': adminToken
      }
    };
    
    const response = await axios.get(`${API_BASE_URL}/admin/users`, config);
    
    console.log('✅ Response status:', response.status);
    console.log('✅ Response data length:', response.data.length);
    console.log('✅ First few users:');
    
    response.data.slice(0, 3).forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.username} (${user.email}) - Active: ${user.isActive}, Admin: ${user.isAdmin}`);
    });
    
    // Check if users have required fields
    const firstUser = response.data[0];
    if (firstUser) {
      console.log('\n🔍 Checking first user structure:');
      console.log('  Has username:', !!firstUser.username);
      console.log('  Has email:', !!firstUser.email);
      console.log('  Has isActive:', firstUser.isActive !== undefined);
      console.log('  Has isAdmin:', firstUser.isAdmin !== undefined);
      console.log('  Has isEmailVerified:', firstUser.isEmailVerified !== undefined);
      console.log('  Has createdAt:', !!firstUser.createdAt);
      console.log('  Has formattedCreatedAt:', !!firstUser.formattedCreatedAt);
      console.log('  Has accountAge:', firstUser.accountAge !== undefined);
    }
    
  } catch (error) {
    console.error('❌ Error testing admin users endpoint:');
    if (error.response) {
      console.error('  Status:', error.response.status);
      console.error('  Data:', error.response.data);
    } else {
      console.error('  Message:', error.message);
    }
    
    if (error.response?.status === 401) {
      console.log('\n💡 To fix this:');
      console.log('1. Log in to the admin dashboard in your browser');
      console.log('2. Open browser dev tools (F12)');
      console.log('3. Go to Application/Storage tab');
      console.log('4. Find localStorage and copy the "token" value');
      console.log('5. Replace "YOUR_ADMIN_TOKEN_HERE" in this script with that token');
    }
  }
}

// Only run if token is provided
const args = process.argv.slice(2);
if (args.length > 0) {
  // Replace the token in the script
  const fs = require('fs');
  const scriptContent = fs.readFileSync(__filename, 'utf8');
  const updatedContent = scriptContent.replace('YOUR_ADMIN_TOKEN_HERE', args[0]);
  eval(updatedContent.split('testAdminUsersEndpoint()')[0] + 'testAdminUsersEndpoint()');
} else {
  console.log('❌ Please provide an admin token as an argument:');
  console.log('node test-admin-users-endpoint.js YOUR_ADMIN_TOKEN');
  console.log('\nTo get your admin token:');
  console.log('1. Log in to the admin dashboard in your browser');
  console.log('2. Open browser dev tools (F12)');
  console.log('3. Go to Application/Storage tab');
  console.log('4. Find localStorage and copy the "token" value');
}

// testAdminUsersEndpoint();
