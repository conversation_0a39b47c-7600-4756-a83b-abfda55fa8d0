const mongoose = require('mongoose');

const ActivitySchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  action: {
    type: String,
    required: true,
    enum: [
      'login', 
      'logout', 
      'create_meal_plan', 
      'update_meal_plan', 
      'delete_meal_plan',
      'create_meal',
      'update_profile'
    ]
  },
  details: {
    type: Object,
    default: {}
  },
  ipAddress: {
    type: String
  }
}, { timestamps: true });

module.exports = mongoose.model('Activity', ActivitySchema);
