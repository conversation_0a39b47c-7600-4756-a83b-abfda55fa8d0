// Update your existing apiService.js or create a new one if it doesn't exist
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Helper function to get the authentication token
const getAuthToken = () => {
  return localStorage.getItem('token');
};

// Add or update this function in your apiService.js
const isAuthenticated = () => {
  const token = localStorage.getItem('token');
  console.log('Checking authentication, token:', token ? 'exists' : 'not found');
  return !!token; // Returns true if token exists, false otherwise
};

// Check authentication status with the server
const checkAuthStatus = async () => {
  try {
    const response = await apiRequest('/users/auth-status');
    return { isAuthenticated: true, user: response.user };
  } catch (error) {
    console.error('Auth status check failed:', error);
    return { isAuthenticated: false };
  }
};

// Update your apiRequest function to better handle errors
const apiRequest = async (endpoint, options = {}) => {
  try {
    // Get the token from localStorage
    const token = localStorage.getItem('token');
    
    // Set default headers
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };
    
    // Add authorization header if token exists
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    // Prepare the request options
    const requestOptions = {
      ...options,
      headers
    };
    
    console.log(`Making ${options.method || 'GET'} request to ${API_BASE_URL}${endpoint}`, {
      hasToken: !!token,
      tokenLength: token ? token.length : 0,
      headers: headers,
      bodyLength: options.body ? options.body.length : 0
    });

    // Make the request
    const response = await fetch(`${API_BASE_URL}${endpoint}`, requestOptions);
    
    // Get the response data
    const data = await response.json();
    
    // Check if the response is ok (status in the range 200-299)
    if (!response.ok) {
      // Add status to the error for specific handling
      const error = new Error(data.message || 'API request failed');
      error.status = response.status;
      error.data = data;
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('API request error:', error);
    
    // Check if the error is a 401 Unauthorized
    if (error.status === 401) {
      // Clear token and user data
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      
      // Redirect to login page if not already there
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }
    
    throw error;
  }
};

// Authentication services
const login = async (email, password) => {
  const response = await apiRequest('/users/login', {
    method: 'POST',
    body: JSON.stringify({ email, password })
  });

  if (response.token) {
    localStorage.setItem('token', response.token);
    if (response.user) {
      localStorage.setItem('user', JSON.stringify(response.user));
    }
  }

  return response;
};

// Update the register function to handle the "User already exists" error
const register = async (userData) => {
  try {
    console.log('Attempting to register user:', userData.email);
    const response = await apiRequest('/users/register', {
      method: 'POST',
      body: JSON.stringify(userData)
    });
    
    if (response.token) {
      localStorage.setItem('token', response.token);
      if (response.user) {
        localStorage.setItem('user', JSON.stringify(response.user));
      }
    }
    
    return response;
  } catch (error) {
    console.error('Registration error:', error.message);
    
    // If the error is "User already exists", suggest logging in instead
    if (error.message === 'User already exists') {
      throw new Error('This email is already registered. Please try logging in instead.');
    }
    
    throw error;
  }
};

const logout = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  // Optionally redirect to login
  // window.location.href = '/login';
};

const getUserProfile = async () => {
  return apiRequest('/users/profile');
};

// MEAL PLAN FUNCTIONS
const getMeals = async () => {
  try {
    const response = await apiRequest('/meals', {
      method: 'GET'
    });
    return response;
  } catch (error) {
    console.error('Error fetching meals:', error);
    throw error;
  }
};

// Get all meal plans for the current user
const getMealPlans = async () => {
  return apiRequest('/meal-plans');
};

// Get a specific meal plan by date
const getMealPlanByDate = async (date) => {
  return apiRequest(`/meal-plans/${date}`);
};

// Create or update a meal plan
const createOrUpdateMealPlan = async (date, mealType, meal) => {
  return apiRequest('/meal-plans', {
    method: 'POST',
    body: JSON.stringify({ date, mealType, meal })
  });
};

// Get meal plan for a specific date
const getMealPlanForDate = async (date) => {
  try {
    console.log('📤 Getting meal plan for date:', date);

    const response = await apiRequest(`/meal-plans/${date}`);

    console.log('📥 Get meal plan response:', {
      hasData: !!response,
      mealPlan: response
    });

    return { success: true, mealPlan: response };
  } catch (error) {
    console.error('❌ Error getting meal plan for date:', error);
    // Return empty response instead of throwing to handle gracefully
    return { success: false, mealPlan: null };
  }
};

// Save complete meal plan (NEW FUNCTION)
const saveMealPlan = async (mealPlanData) => {
  try {
    console.log('📤 Sending meal plan save request:', {
      name: mealPlanData.name,
      startDate: mealPlanData.startDate,
      endDate: mealPlanData.endDate,
      mealsCount: mealPlanData.meals?.length,
      hasRiceBowls: !!mealPlanData.riceBowlsPerDay,
      hasMealTimes: !!mealPlanData.mealTimes,
      fullData: JSON.stringify(mealPlanData, null, 2)
    });

    const response = await apiRequest('/meal-plans/save', {
      method: 'POST',
      body: JSON.stringify(mealPlanData)
    });

    console.log('📥 Meal plan save response:', {
      success: response.success,
      message: response.message,
      hasData: !!response.data,
      fullResponse: response
    });

    return response;
  } catch (error) {
    console.error('❌ Error saving meal plan:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
      fullError: error
    });
    throw error;
  }
};

// Validate meal plan before saving (NEW FUNCTION)
const validateMealPlan = async (selectedMeals) => {
  return apiRequest('/meal-plans/validate', {
    method: 'POST',
    body: JSON.stringify({ selectedMeals })
  });
};

// Get saved meal plans (NEW FUNCTION)
const getSavedMealPlans = async () => {
  return apiRequest('/meal-plans/saved');
};

// Remove a meal from a plan
const removeMealFromPlan = async (date, mealType, mealInstanceId) => {
  return apiRequest(`/meal-plans/${date}/meals`, {
    method: 'DELETE',
    body: JSON.stringify({ mealType, mealInstanceId })
  });
};

// Mark a meal as completed or incomplete
const markMealCompleted = async (date, mealType, mealInstanceId, isCompleted) => {
  return apiRequest(`/meal-plans/${date}/complete`, {
    method: 'PUT',
    body: JSON.stringify({ mealType, mealInstanceId, isCompleted })
  });
};

// Toggle the lock status of a meal plan
const toggleLockMealPlan = async (date, isLocked) => {
  return apiRequest(`/meal-plans/${date}/lock`, {
    method: 'PUT',
    body: JSON.stringify({ isLocked })
  });
};

// Update meal times
const updateMealTimes = async (date, mealTimes) => {
  return apiRequest(`/meal-plans/${date}/meal-times`, {
    method: 'PUT',
    body: JSON.stringify({ mealTimes })
  });
};

// Generate meal plan automatically (NEW FUNCTION)
const generateMealPlan = async (generateData) => {
  try {
    console.log('🤖 Sending meal plan generation request:', generateData);
    const response = await apiRequest('/meal-plans/generate', {
      method: 'POST',
      body: JSON.stringify(generateData)
    });
    console.log('📥 Meal plan generation response:', response);
    return response;
  } catch (error) {
    console.error('❌ Error generating meal plan:', error);
    throw error;
  }
};

// Export all services
const apiService = {
  // Auth
  login,
  register,
  logout,
  isAuthenticated,
  checkAuthStatus,
  getUserProfile,

  // Meal Plans
  getMealPlans,
  getMealPlanByDate,
  getMealPlanForDate, // NEW
  createOrUpdateMealPlan,
  saveMealPlan, // NEW
  validateMealPlan, // NEW
  getSavedMealPlans, // NEW
  generateMealPlan, // NEW
  removeMealFromPlan,
  markMealCompleted,
  toggleLockMealPlan,
  updateMealTimes,
  getMeals,

  // Helper for custom API calls
  apiRequest
};

export default apiService;
