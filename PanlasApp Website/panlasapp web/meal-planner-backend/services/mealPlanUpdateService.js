const MealPlan = require('../models/MealPlan');
const Meal = require('../models/Meal');
const geminiService = require('./geminiService');

/**
 * Updates future meal plans when dietary preferences change
 * @param {string} userId - User ID
 * @param {Object} newDietaryPreferences - New dietary preferences
 * @returns {Object} Update result with statistics
 */
async function updateFutureMealPlansForDietaryChanges(userId, newDietaryPreferences) {
  try {
    console.log(`Starting meal plan updates for user ${userId}`);
    
    // Get today's date in YYYY-MM-DD format
    const today = new Date().toISOString().split('T')[0];
    
    // Find all future meal plans (from today onwards)
    const futureMealPlans = await MealPlan.find({
      user: userId,
      date: { $gte: today },
      isTemplate: false // Don't update templates
    }).sort({ date: 1 });
    
    console.log(`Found ${futureMealPlans.length} future meal plans to check`);
    
    if (futureMealPlans.length === 0) {
      return {
        success: true,
        message: 'No future meal plans to update',
        plansChecked: 0,
        plansUpdated: 0,
        mealsReplaced: 0
      };
    }
    
    let plansUpdated = 0;
    let totalMealsReplaced = 0;
    const updateDetails = [];
    
    // Process each meal plan
    for (const mealPlan of futureMealPlans) {
      console.log(`Checking meal plan for date: ${mealPlan.date}`);
      
      const planUpdateResult = await updateSingleMealPlan(mealPlan, newDietaryPreferences);
      
      if (planUpdateResult.updated) {
        plansUpdated++;
        totalMealsReplaced += planUpdateResult.mealsReplaced;
        updateDetails.push({
          date: mealPlan.date,
          mealsReplaced: planUpdateResult.mealsReplaced,
          replacements: planUpdateResult.replacements
        });
      }
    }
    
    console.log(`Meal plan update completed. Plans updated: ${plansUpdated}, Total meals replaced: ${totalMealsReplaced}`);
    
    return {
      success: true,
      message: `Updated ${plansUpdated} meal plans with ${totalMealsReplaced} meal replacements`,
      plansChecked: futureMealPlans.length,
      plansUpdated,
      mealsReplaced: totalMealsReplaced,
      updateDetails
    };
    
  } catch (error) {
    console.error('Error updating future meal plans:', error);
    throw error;
  }
}

/**
 * Updates a single meal plan based on new dietary preferences
 * @param {Object} mealPlan - Meal plan to update
 * @param {Object} dietaryPreferences - New dietary preferences
 * @returns {Object} Update result for this plan
 */
async function updateSingleMealPlan(mealPlan, dietaryPreferences) {
  const mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];
  let planUpdated = false;
  let mealsReplaced = 0;
  const replacements = [];
  
  for (const mealType of mealTypes) {
    if (mealPlan[mealType] && mealPlan[mealType].length > 0) {
      const mealTypeResult = await updateMealTypeInPlan(
        mealPlan, 
        mealType, 
        dietaryPreferences
      );
      
      if (mealTypeResult.updated) {
        planUpdated = true;
        mealsReplaced += mealTypeResult.mealsReplaced;
        replacements.push(...mealTypeResult.replacements);
      }
    }
  }
  
  if (planUpdated) {
    mealPlan.updatedAt = new Date();
    await mealPlan.save();
  }
  
  return {
    updated: planUpdated,
    mealsReplaced,
    replacements
  };
}

/**
 * Updates meals of a specific type in a meal plan using AI
 * @param {Object} mealPlan - Meal plan to update
 * @param {string} mealType - Type of meal (breakfast, lunch, dinner, snack)
 * @param {Object} dietaryPreferences - New dietary preferences
 * @returns {Object} Update result for this meal type
 */
async function updateMealTypeInPlan(mealPlan, mealType, dietaryPreferences) {
  const meals = mealPlan[mealType];

  if (!meals || meals.length === 0) {
    return { updated: false, mealsReplaced: 0, replacements: [] };
  }

  console.log(`Asking AI to review ${meals.length} ${mealType} meals for dietary preference changes...`);

  // Get all available meals for this meal type from database (limited for performance)
  const availableMeals = await Meal.find({ category: mealType.toLowerCase() }).limit(50);

  if (availableMeals.length === 0) {
    console.log(`No available meals found in database for ${mealType}`);
    return { updated: false, mealsReplaced: 0, replacements: [] };
  }

  // Ask AI to review and replace meals based on new dietary preferences (with timeout)
  const aiReplacementResult = await Promise.race([
    getAIReplacementsForMealType(meals, mealType, dietaryPreferences, availableMeals),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error('AI analysis timeout')), 30000) // 30 second timeout
    )
  ]).catch(error => {
    console.warn(`AI analysis failed for ${mealType}:`, error.message);
    return null; // Continue without AI suggestions
  });

  if (!aiReplacementResult || !aiReplacementResult.replacements) {
    console.log(`AI did not suggest any replacements for ${mealType}`);
    return { updated: false, mealsReplaced: 0, replacements: [] };
  }

  // Apply AI-suggested replacements
  let mealsReplaced = 0;
  const replacements = [];
  let updated = false;

  for (const replacement of aiReplacementResult.replacements) {
    const mealIndex = meals.findIndex(meal => meal.name === replacement.originalMeal);

    if (mealIndex !== -1) {
      // Find the replacement meal in available meals
      const newMeal = availableMeals.find(meal =>
        meal.name.toLowerCase() === replacement.newMeal.toLowerCase()
      );

      if (newMeal) {
        const oldMealName = meals[mealIndex].name;

        // Replace with new meal (handle category array properly)
        meals[mealIndex] = {
          name: newMeal.name,
          mealType: Array.isArray(newMeal.mealType) ? newMeal.mealType[0] : newMeal.mealType,
          category: Array.isArray(newMeal.category) ? newMeal.category[0] : newMeal.category,
          dietaryTags: newMeal.dietaryTags || [],
          calories: newMeal.calories,
          protein: newMeal.protein,
          carbs: newMeal.carbs,
          fat: newMeal.fat,
          image: newMeal.image,
          description: newMeal.description,
          ingredients: newMeal.ingredients || [],
          instructions: newMeal.instructions || [],
          instanceId: `ai_replaced_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          aiReason: replacement.reason
        };

        mealsReplaced++;
        updated = true;

        replacements.push({
          mealType,
          oldMeal: oldMealName,
          newMeal: newMeal.name,
          reason: replacement.reason
        });

        console.log(`AI replaced "${oldMealName}" with "${newMeal.name}" in ${mealType}: ${replacement.reason}`);
      }
    }
  }

  return {
    updated,
    mealsReplaced,
    replacements
  };
}

/**
 * Checks if a meal conflicts with dietary preferences
 * @param {Object} meal - Meal to check
 * @param {Object} dietaryPreferences - Dietary preferences to check against
 * @returns {Object} Conflict result with reasons
 */
function checkMealConflict(meal, dietaryPreferences) {
  const conflicts = [];

  // Check dietary restrictions (user wants ONLY these types)
  if (dietaryPreferences.restrictions && dietaryPreferences.restrictions.length > 0) {
    for (const restriction of dietaryPreferences.restrictions) {
      if (!mealMeetsRestriction(meal, restriction)) {
        conflicts.push(`Does not meet ${restriction} requirement`);
      }
    }
  }

  // Check allergies (user does NOT want these)
  if (dietaryPreferences.allergies && dietaryPreferences.allergies.length > 0) {
    for (const allergy of dietaryPreferences.allergies) {
      if (mealContainsAllergen(meal, allergy)) {
        conflicts.push(`Contains allergen: ${allergy}`);
      }
    }
  }

  // Note: Removed disliked ingredients and calorie checking for simplicity

  return {
    hasConflict: conflicts.length > 0,
    reasons: conflicts
  };
}

/**
 * Checks if a meal meets a dietary restriction
 * @param {Object} meal - Meal to check
 * @param {string} restriction - Dietary restriction
 * @returns {boolean} Whether meal meets the restriction
 */
function mealMeetsRestriction(meal, restriction) {
  const mealTags = meal.dietaryTags || [];
  const dietType = meal.dietType || {};
  
  switch (restriction.toLowerCase()) {
    case 'vegetarian':
      return dietType.isVegetarian === true || mealTags.includes('vegetarian');
    case 'vegan':
      return dietType.isVegan === true || mealTags.includes('vegan');
    case 'gluten-free':
      return dietType.isGlutenFree === true || mealTags.includes('gluten-free');
    case 'dairy-free':
      return dietType.isDairyFree === true || mealTags.includes('dairy-free');
    case 'nut-free':
      return dietType.isNutFree === true || mealTags.includes('nut-free');
    case 'low-carb':
      return mealTags.includes('low-carb') || (meal.carbs && meal.carbs < 20);
    case 'keto':
      // For keto, prioritize carb content over tags
      if (meal.carbs !== undefined && meal.carbs !== null) {
        return meal.carbs <= 10; // Keto requires ≤10g carbs
      }
      return dietType.isKeto === true || mealTags.includes('keto');
    case 'paleo':
      return dietType.isPaleo === true || mealTags.includes('paleo');
    default:
      return true; // Unknown restriction, assume it's met
  }
}

/**
 * Checks if a meal contains an allergen
 * @param {Object} meal - Meal to check
 * @param {string} allergen - Allergen to check for
 * @returns {boolean} Whether meal contains the allergen
 */
function mealContainsAllergen(meal, allergen) {
  const allergens = meal.allergens || [];
  const ingredients = meal.ingredients || [];
  const mealName = meal.name || '';
  
  // Check explicit allergens list
  if (allergens.some(a => a.toLowerCase().includes(allergen.toLowerCase()))) {
    return true;
  }
  
  // Check ingredients
  if (ingredients.some(ing => ing.toLowerCase().includes(allergen.toLowerCase()))) {
    return true;
  }
  
  // Check meal name
  if (mealName.toLowerCase().includes(allergen.toLowerCase())) {
    return true;
  }
  
  return false;
}

/**
 * Checks if a meal contains a disliked ingredient
 * @param {Object} meal - Meal to check
 * @param {string} ingredient - Ingredient to check for
 * @returns {boolean} Whether meal contains the ingredient
 */
function mealContainsIngredient(meal, ingredient) {
  const ingredients = meal.ingredients || [];
  const mealName = meal.name || '';
  
  // Check ingredients list
  if (ingredients.some(ing => ing.toLowerCase().includes(ingredient.toLowerCase()))) {
    return true;
  }
  
  // Check meal name
  if (mealName.toLowerCase().includes(ingredient.toLowerCase())) {
    return true;
  }
  
  return false;
}

/**
 * Gets AI-powered replacements for multiple meals of a specific type
 * @param {Array} currentMeals - Current meals in the meal plan
 * @param {string} mealType - Type of meal (breakfast, lunch, dinner, snack)
 * @param {Object} dietaryPreferences - New dietary preferences
 * @param {Array} availableMeals - All available meals from database
 * @returns {Object|null} AI replacement suggestions
 */
async function getAIReplacementsForMealType(currentMeals, mealType, dietaryPreferences, availableMeals) {
  try {
    console.log(`Asking AI to review ${currentMeals.length} ${mealType} meals against new dietary preferences...`);

    // Create detailed meal lists for AI
    const currentMealsList = currentMeals.map((meal, index) => `
    ${index + 1}. "${meal.name}"
       - Calories: ${meal.calories || 'N/A'}, Protein: ${meal.protein || 'N/A'}g, Carbs: ${meal.carbs || 'N/A'}g, Fat: ${meal.fat || 'N/A'}g
       - Dietary Tags: ${meal.dietaryTags?.join(', ') || 'None'}
       - Allergens: ${meal.allergens?.join(', ') || 'None'}
       - Ingredients: ${meal.ingredients?.slice(0, 5).join(', ') || 'N/A'}${meal.ingredients?.length > 5 ? '...' : ''}
    `).join('');

    const availableMealsList = availableMeals.map((meal, index) => `
    ${index + 1}. "${meal.name}"
       - Calories: ${meal.calories || 'N/A'}, Protein: ${meal.protein || 'N/A'}g, Carbs: ${meal.carbs || 'N/A'}g, Fat: ${meal.fat || 'N/A'}g
       - Dietary Tags: ${meal.dietaryTags?.join(', ') || 'None'}
       - Allergens: ${meal.allergens?.join(', ') || 'None'}
       - Vegetarian: ${meal.dietType?.isVegetarian ? 'Yes' : 'No'}
       - Vegan: ${meal.dietType?.isVegan ? 'Yes' : 'No'}
       - Gluten-Free: ${meal.dietType?.isGlutenFree ? 'Yes' : 'No'}
       - Keto: ${meal.dietType?.isKeto ? 'Yes' : 'No'}
       - Low-Carb: ${meal.dietType?.isLowCarb ? 'Yes' : 'No'}
    `).join('');

    const prompt = `
You are a nutritionist AI helping update a meal plan based on new dietary preferences.

NEW DIETARY PREFERENCES:
- Restrictions: ${dietaryPreferences.restrictions?.join(', ') || 'None'}
- Allergies: ${dietaryPreferences.allergies?.join(', ') || 'None'}

CURRENT ${mealType.toUpperCase()} MEALS:
${currentMealsList}

AVAILABLE REPLACEMENT MEALS (you can ONLY choose from these):
${availableMealsList}

TASK:
1. Review each current meal against the NEW dietary preferences
2. Identify which meals need to be replaced (don't meet new restrictions or contain allergens)
3. For each meal that needs replacement, suggest the BEST alternative from the available meals
4. If a meal already meets the new preferences, keep it (no replacement needed)

Return your response in this JSON format:
{
  "analysis": "brief explanation of what you found",
  "replacements": [
    {
      "originalMeal": "EXACT name of meal to replace",
      "newMeal": "EXACT name from available meals list",
      "reason": "why this meal needs to be replaced and why the new meal is better"
    }
  ]
}

IMPORTANT RULES:
- Only suggest replacements for meals that truly conflict with the new dietary preferences
- Only use meal names that appear EXACTLY in the available meals list
- If no replacements are needed, return empty replacements array
- Be specific about why each replacement is necessary

Only return the JSON, no additional text.
`;

    const response = await geminiService.generateContent(prompt);
    const cleanedResponse = geminiService.extractJsonFromResponse(response);
    const aiResponse = JSON.parse(cleanedResponse);

    console.log(`AI analysis: ${aiResponse.analysis}`);
    console.log(`AI suggested ${aiResponse.replacements?.length || 0} replacements for ${mealType}`);

    // Validate that all suggested meals exist in available meals
    if (aiResponse.replacements) {
      aiResponse.replacements = aiResponse.replacements.filter(replacement => {
        const mealExists = availableMeals.some(meal =>
          meal.name.toLowerCase() === replacement.newMeal.toLowerCase()
        );
        if (!mealExists) {
          console.warn(`AI suggested non-existent meal: ${replacement.newMeal}`);
          return false;
        }
        return true;
      });
    }

    return aiResponse;

  } catch (error) {
    console.error('Error getting AI meal replacements:', error);
    return null;
  }
}

/**
 * Gets AI-powered replacement meal suggestion (legacy function, kept for compatibility)
 * @param {Object} originalMeal - Original meal to replace
 * @param {string} mealType - Type of meal
 * @param {Object} dietaryPreferences - Dietary preferences
 * @returns {Object|null} Replacement meal or null if none found
 */
async function getAIReplacementMeal(originalMeal, mealType, dietaryPreferences) {
  try {
    // Get all available meals that meet the dietary preferences
    const availableMeals = await getFilteredMeals(mealType, dietaryPreferences);

    if (availableMeals.length === 0) {
      console.log(`No suitable replacement meals found for ${mealType}`);
      return null;
    }

    // Use AI to get the best replacement with the new specialized method
    const aiReplacementResult = await geminiService.generateMealReplacement(
      originalMeal,
      { dietaryPreferences },
      availableMeals,
      mealType
    );

    if (aiReplacementResult && aiReplacementResult.replacement) {
      const recommendedMeal = aiReplacementResult.replacement;

      // Find the actual meal object
      const replacementMeal = availableMeals.find(meal =>
        meal.name.toLowerCase() === recommendedMeal.mealName.toLowerCase()
      );

      if (replacementMeal) {
        console.log(`AI recommended "${replacementMeal.name}" as replacement for "${originalMeal.name}": ${recommendedMeal.reason}`);

        // Convert to meal plan format (handle arrays properly)
        return {
          name: replacementMeal.name,
          mealType: Array.isArray(replacementMeal.mealType) ? replacementMeal.mealType[0] : replacementMeal.mealType,
          category: Array.isArray(replacementMeal.category) ? replacementMeal.category[0] : replacementMeal.category,
          dietaryTags: replacementMeal.dietaryTags || [],
          calories: replacementMeal.calories,
          protein: replacementMeal.protein,
          carbs: replacementMeal.carbs,
          fat: replacementMeal.fat,
          image: replacementMeal.image,
          description: replacementMeal.description,
          ingredients: replacementMeal.ingredients || [],
          instructions: replacementMeal.instructions || [],
          instanceId: `replaced_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          aiReason: recommendedMeal.reason,
          nutritionalComparison: recommendedMeal.nutritionalComparison
        };
      }
    }

    // Fallback: use general meal recommendations
    const aiRecommendation = await geminiService.generateMealRecommendations(
      { dietaryPreferences },
      availableMeals,
      `replacement_for_${originalMeal.name}`
    );

    if (aiRecommendation.recommendations && aiRecommendation.recommendations.length > 0) {
      const recommendedMeal = aiRecommendation.recommendations[0];

      // Find the actual meal object
      const replacementMeal = availableMeals.find(meal =>
        meal.name.toLowerCase() === recommendedMeal.mealName.toLowerCase()
      );

      if (replacementMeal) {
        console.log(`AI fallback recommended "${replacementMeal.name}" as replacement for "${originalMeal.name}"`);

        // Convert to meal plan format (handle arrays properly)
        return {
          name: replacementMeal.name,
          mealType: Array.isArray(replacementMeal.mealType) ? replacementMeal.mealType[0] : replacementMeal.mealType,
          category: Array.isArray(replacementMeal.category) ? replacementMeal.category[0] : replacementMeal.category,
          dietaryTags: replacementMeal.dietaryTags || [],
          calories: replacementMeal.calories,
          protein: replacementMeal.protein,
          carbs: replacementMeal.carbs,
          fat: replacementMeal.fat,
          image: replacementMeal.image,
          description: replacementMeal.description,
          ingredients: replacementMeal.ingredients || [],
          instructions: replacementMeal.instructions || [],
          instanceId: `replaced_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };
      }
    }

    // Last fallback: return first suitable meal
    if (availableMeals.length > 0) {
      const fallbackMeal = availableMeals[0];
      console.log(`Using first available meal "${fallbackMeal.name}" as replacement for "${originalMeal.name}"`);

      return {
        name: fallbackMeal.name,
        mealType: Array.isArray(fallbackMeal.mealType) ? fallbackMeal.mealType[0] : fallbackMeal.mealType,
        category: Array.isArray(fallbackMeal.category) ? fallbackMeal.category[0] : fallbackMeal.category,
        dietaryTags: fallbackMeal.dietaryTags || [],
        calories: fallbackMeal.calories,
        protein: fallbackMeal.protein,
        carbs: fallbackMeal.carbs,
        fat: fallbackMeal.fat,
        image: fallbackMeal.image,
        description: fallbackMeal.description,
        ingredients: fallbackMeal.ingredients || [],
        instructions: fallbackMeal.instructions || [],
        instanceId: `replaced_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
    }

    return null;

  } catch (error) {
    console.error('Error getting AI replacement meal:', error);
    return null;
  }
}

/**
 * Gets filtered meals based on dietary preferences and meal type
 * @param {string} mealType - Type of meal
 * @param {Object} dietaryPreferences - Dietary preferences
 * @returns {Array} Filtered meals
 */
async function getFilteredMeals(mealType, dietaryPreferences) {
  try {
    // Build query based on preferences
    const query = { category: mealType.toLowerCase() };

    // Handle dietary restrictions (inclusive filtering)
    if (dietaryPreferences.restrictions && dietaryPreferences.restrictions.length > 0) {
      const dietaryConditions = [];

      dietaryPreferences.restrictions.forEach(restriction => {
        switch(restriction.toLowerCase()) {
          case 'vegetarian':
            dietaryConditions.push({ 'dietType.isVegetarian': true });
            break;
          case 'vegan':
            dietaryConditions.push({ 'dietType.isVegan': true });
            break;
          case 'gluten-free':
            dietaryConditions.push({ 'dietType.isGlutenFree': true });
            break;
          case 'dairy-free':
            dietaryConditions.push({ 'dietType.isDairyFree': true });
            break;
          case 'nut-free':
            dietaryConditions.push({ 'dietType.isNutFree': true });
            break;
          case 'keto':
            // For keto, prioritize carb content over tags
            dietaryConditions.push({
              $or: [
                { 'dietType.isKeto': true },
                { carbs: { $lte: 10 } } // Keto requires ≤10g carbs
              ]
            });
            break;
          case 'paleo':
            dietaryConditions.push({ 'dietType.isPaleo': true });
            break;
          case 'low-carb':
            dietaryConditions.push({ carbs: { $lt: 20 } });
            break;
        }
      });

      if (dietaryConditions.length > 0) {
        query.$and = dietaryConditions;
      }
    }

    // Handle allergies (exclusive filtering)
    if (dietaryPreferences.allergies && dietaryPreferences.allergies.length > 0) {
      const allergenConditions = [];

      dietaryPreferences.allergies.forEach(allergy => {
        allergenConditions.push({
          $and: [
            { allergens: { $not: { $regex: allergy, $options: 'i' } } },
            { ingredients: { $not: { $elemMatch: { $regex: allergy, $options: 'i' } } } },
            { name: { $not: { $regex: allergy, $options: 'i' } } }
          ]
        });
      });

      if (allergenConditions.length > 0) {
        if (query.$and) {
          query.$and.push(...allergenConditions);
        } else {
          query.$and = allergenConditions;
        }
      }
    }

    // REMOVED: Disliked ingredients filtering (simplified logic)

    // REMOVED: Calorie filtering (simplified logic)

    console.log('Filtering meals with simplified query:', JSON.stringify(query, null, 2));

    const meals = await Meal.find(query).limit(100); // Increased limit for more options
    console.log(`Found ${meals.length} meals matching dietary preferences for ${mealType}`);

    return meals;

  } catch (error) {
    console.error('Error filtering meals:', error);
    return [];
  }
}

module.exports = {
  updateFutureMealPlansForDietaryChanges,
  updateSingleMealPlan,
  checkMealConflict,
  getAIReplacementMeal,
  getAIReplacementsForMealType,
  getFilteredMeals
};
