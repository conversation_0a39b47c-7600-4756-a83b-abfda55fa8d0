import api from './api';

// Sample data as fallback
const sampleFilipinoDishes = [
  {
    id: 1,
    name: "<PERSON>ob<PERSON>",
    category: "Main Course",
    description: "A popular Filipino dish of meat marinated in vinegar, soy sauce, and spices.",
    image: "https://example.com/adobo.jpg",
    rating: 4.8,
    calories: 450,
    protein: 30,
    carbs: 15,
    fat: 25,
    prepTime: 45,
    ingredients: ["Chicken", "Soy Sauce", "Vinegar", "Garlic", "Bay Leaves", "Peppercorns"],
    instructions: ["Marinate chicken", "Brown in pan", "Add marinade", "Simmer until tender"],
    dietaryTags: ["High Protein", "Gluten-Free"],
    mealType: ["Lunch", "Dinner"]
  },
  {
    id: 2,
    name: "Sinigang",
    category: "Soup",
    description: "A sour Filipino soup with meat and vegetables.",
    image: "https://via.placeholder.com/300x200?text=Sinigang",
    rating: 4.7,
    calories: 380,
    protein: 25,
    carbs: 20,
    fat: 18,
    prepTime: 60,
    ingredients: ["Pork", "Tamarind", "Tomatoes", "Onions", "String beans", "Eggplant"],
    instructions: ["Sauté onions and tomatoes", "Add meat and cook", "Add water and tamarind", "Add vegetables"],
    dietaryTags: ["Gluten-Free"],
    mealType: ["Lunch", "Dinner"]
  }
];

// Get all Filipino dishes
export const getFilipinoDishes = async () => {
  try {
    console.log("Attempting to fetch Filipino dishes from API...");
    const response = await api.get('api/meals');
    console.log("API response received:", response);
    return response.data;
  } catch (error) {
    console.error('Error fetching Filipino dishes:', error);
    
    // Log detailed error information
    if (error.response) {
      console.error("Response data:", error.response.data);
      console.error("Response status:", error.response.status);
    } else if (error.request) {
      console.error("No response received:", error.request);
    } else {
      console.error("Error message:", error.message);
    }
    
    console.log("Using fallback data instead");
    // Return fallback data instead of throwing an error
    return sampleFilipinoDishes;
  }
};

// Get all meals
export const getAllMeals = async () => {
  try {
    const response = await api.get('/meals');
    return response.data;
  } catch (error) {
    console.error('Error fetching meals:', error);
    throw error;
  }
};

// Get meal by ID
export const getMealById = async (mealId) => {
  try {
    const response = await api.get(`/meals/${mealId}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching meal with ID ${mealId}:`, error);
    throw error;
  }
};

// Create a new meal
export const createMeal = async (mealData) => {
  try {
    const response = await api.post('/meals', mealData);
    return response.data;
  } catch (error) {
    console.error('Error creating meal:', error);
    throw error;
  }
};

// Update a meal
export const updateMeal = async (mealId, mealData) => {
  try {
    const response = await api.put(`/meals/${mealId}`, mealData);
    return response.data;
  } catch (error) {
    console.error(`Error updating meal with ID ${mealId}:`, error);
    throw error;
  }
};

// Delete a meal
export const deleteMeal = async (mealId) => {
  try {
    const response = await api.delete(`/meals/${mealId}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting meal with ID ${mealId}:`, error);
    throw error;
  }
};

// Get Filipino dish by ID
export const getFilipinoDishById = async (dishId) => {
  try {
    const response = await api.get(`/meals/filipino/dishes/${dishId}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching Filipino dish with ID ${dishId}:`, error);
    throw error;
  }
};
