const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const auth = require('../middleware/auth');
const { adminAuth } = require('../middleware/adminAuth');

console.log('=== ADMIN ROUTES FILE LOADED ===');

// Apply auth middleware to all admin routes
router.use(auth);

// Get all users (admin only)
router.get('/users', adminController.getAllUsers);

// User management routes
console.log('Setting up user management routes...');
router.post('/users', adminController.createUser);
router.put('/users/:userId', adminController.updateUser);
console.log('Setting up disable/enable routes...');
router.put('/users/:userId/disable', adminController.disableUser);
router.put('/users/:userId/enable', adminController.enableUser);
console.log('Disable/enable routes set up');
router.put('/users/:userId/make-admin', adminController.makeUserAdmin);
router.put('/users/:userId/remove-admin', adminController.removeUserAdmin);
router.put('/users/:userId/update-role', adminController.updateAdminRole);
router.get('/users/:userId/admin-info', adminController.getAdminInfo);

// Get user signup statistics (admin only)
router.get('/stats/signups', adminController.getUserSignupStats);

// Get system overview (admin only)
router.get('/overview', adminController.getSystemOverview);

// Generate user registration report
router.get('/reports/registrations', adminController.getUserRegistrationReport);

// Get system health information
router.get('/system/health', adminController.getSystemHealth);

// Geolocation analytics
router.get('/analytics/geolocation', adminController.getGeolocationAnalytics);

console.log('=== ADMIN ROUTES SETUP COMPLETE ===');
module.exports = router;
