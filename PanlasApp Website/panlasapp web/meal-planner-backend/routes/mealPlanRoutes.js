const express = require('express');
const router = express.Router();
const mealPlanController = require('../controllers/mealPlanController');
const auth = require('../middleware/auth'); // Add auth middleware
const { mealPlanCreateLogger, mealPlanUpdateLogger, mealPlanDeleteLogger } = require('../middleware/activityLogger');

// GET all meal plans for a user
router.get('/', auth.authWithTerms, mealPlanController.getMealPlans);

// Get saved meal plan templates (NEW ROUTE)
router.get('/saved', auth.authWithTerms, mealPlanController.getSavedMealPlans);

// Get specific meal plan by ID (NEW ROUTE)
router.get('/saved/:id', auth.authWithTerms, mealPlanController.getMealPlanById);

// Get specific meal plan by date
router.get('/:date', auth.authWithTerms, mealPlanController.getMealPlanByDate);

// Create or update meal plan with activity logging
router.post('/', auth.authWithTerms, mealPlanCreateLogger, mealPlanController.createOrUpdateMealPlan);

// Save complete meal plan (NEW ROUTE)
router.post('/save', auth.authWithTerms, mealPlanController.saveMealPlan);

// Generate meal plan automatically (NEW ROUTE)
router.post('/generate', auth.authWithTerms, mealPlanController.generateMealPlan);

// Update meal plan lock status
router.put('/:date/lock', auth.authWithTerms, mealPlanController.toggleLockMealPlan);

// TEMPORARY: Test route without auth for debugging
router.put('/:date/lock-test', (req, res) => {
  console.log('🧪 Test route hit:', req.params.date, req.body);
  res.json({
    success: true,
    message: 'Test route working',
    date: req.params.date,
    body: req.body
  });
});

// Mark meal as completed
router.put('/:date/complete', auth.authWithTerms, mealPlanController.markMealCompleted);

// Remove meal from plan
router.delete('/:date/meals', auth.authWithTerms, mealPlanController.removeMealFromPlan);

// Delete entire meal plan for a date with activity logging
router.delete('/:date', auth.authWithTerms, mealPlanDeleteLogger, mealPlanController.deleteMealPlan);

// These already have auth
router.post('/from-template', auth.authWithTerms, mealPlanController.createFromTemplate);
router.post('/create-template', auth.authWithTerms, mealPlanController.createTemplateFromMealPlan);
router.get('/recommendations', auth.authWithTerms, mealPlanController.getMealRecommendations);
router.put('/:date/meal-times', auth.authWithTerms, mealPlanController.updateMealTimes);
router.get('/user/plans', auth.authWithTerms, mealPlanController.getUserMealPlans);

// New endpoints for dietary preference updates
router.post('/update-for-dietary-changes', auth.authWithTerms, mealPlanController.updateMealPlansForDietaryChanges);
router.get('/dietary-conflicts', auth.authWithTerms, mealPlanController.checkDietaryConflicts);
router.post('/resolve-conflicts', auth.authWithTerms, mealPlanController.resolveDietaryConflicts);

// Validate meal plan before saving
router.post('/validate', auth.authWithTerms, mealPlanController.validateMealPlan);

module.exports = router;
