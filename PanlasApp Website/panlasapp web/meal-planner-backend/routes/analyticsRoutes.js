const express = require('express');
const router = express.Router();
const analyticsController = require('../controllers/analyticsController');
const auth = require('../middleware/auth');
const { adminAuth } = require('../middleware/adminAuth');
const { trackAnalytics } = require('../middleware/analytics');

// Admin-only analytics routes
router.get('/dashboard', auth, adminAuth, analyticsController.getDashboardAnalytics);
router.get('/users', auth, adminAuth, analyticsController.getUserActivityAnalytics);
router.get('/platforms', auth, adminAuth, analyticsController.getPlatformAnalytics);

// User event tracking (authenticated users only)
router.post('/track', auth, trackAnalytics('custom_event'), analyticsController.trackEvent);

module.exports = router;
