# PanlasApp Backend - Meal Planner API

Express.js backend API for the PanlasApp Filipino meal planning application.

## Features

- User authentication and authorization
- Meal management and recommendations
- Meal plan creation and tracking
- Admin dashboard functionality
- Analytics and feedback system
- Password reset functionality
- AI-powered meal suggestions

## Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **Password Hashing**: bcryptjs
- **Email**: <PERSON>demailer
- **CORS**: cors middleware
- **Environment**: dotenv

## Quick Start

### Local Development

1. **Clone and navigate to backend directory**:
   ```bash
   cd "PanlasApp Website/panlasapp web/meal-planner-backend"
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   Edit `.env` with your local configuration.

4. **Start MongoDB** (if running locally):
   ```bash
   mongod
   ```

5. **Seed the database** (optional):
   ```bash
   npm run seed
   ```

6. **Start the development server**:
   ```bash
   npm run dev
   ```

The API will be available at `http://localhost:5000`

### Production Deployment (Railway)

1. **Deploy to Railway**:
   - Connect your GitHub repository to Railway
   - Select this backend directory as the root
   - Railway will automatically detect the Node.js project

2. **Configure environment variables** in Railway dashboard:
   - `NODE_ENV=production`
   - `MONGODB_URI=your-mongodb-connection-string`
   - `JWT_SECRET=your-jwt-secret`
   - `FRONTEND_URL=your-frontend-domain`
   - See `.env.example` for all required variables

3. **Deploy**: Railway will automatically build and deploy your application

## API Endpoints

### Authentication
- `POST /api/users/register` - User registration
- `POST /api/users/login` - User login
- `POST /api/password-reset/request` - Request password reset
- `POST /api/password-reset/reset` - Reset password

### Meals
- `GET /api/meals` - Get all meals
- `GET /api/meals/:id` - Get specific meal
- `POST /api/meals` - Create new meal (admin)
- `PUT /api/meals/:id` - Update meal (admin)
- `DELETE /api/meals/:id` - Delete meal (admin)

### Meal Plans
- `GET /api/meal-plans` - Get user's meal plans
- `GET /api/meal-plans/date/:date` - Get meal plan for specific date
- `POST /api/meal-plans` - Create/update meal plan
- `PATCH /api/meal-plans/lock/:date` - Toggle meal plan lock
- `DELETE /api/meal-plans` - Delete meal plan

### Admin
- `GET /api/admin/users` - Get all users (admin)
- `GET /api/admin/analytics` - Get analytics data (admin)
- `POST /api/admin/meals` - Create meal (admin)

### Other
- `GET /api/analytics` - Get analytics data
- `POST /api/feedback` - Submit feedback
- `GET /api/activity` - Get user activity

## Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run seed` - Seed database with initial data
- `npm run migrate-meal-plans` - Migrate meal plans
- `npm run check-meal-plans` - Check meal plan integrity
- `npm run delete-users` - Interactive user deletion
- `npm run fix-meal-plans` - Fix meal plan issues
- `npm run fix-indexes` - Fix database indexes

## Environment Variables

See `.env.example` for a complete list of required environment variables.

Key variables:
- `MONGODB_URI` - MongoDB connection string
- `JWT_SECRET` - Secret for JWT token signing
- `PORT` - Server port (default: 5000)
- `NODE_ENV` - Environment (development/production)
- `FRONTEND_URL` - Frontend URL for CORS and email links

## Database Schema

### User
- Authentication and profile information
- Dietary preferences and restrictions
- Activity tracking

### Meal
- Filipino meal data with nutritional information
- Categories, ingredients, and preparation details
- Pricing and dietary classifications

### MealPlan
- User's planned meals by date and meal type
- Completion tracking and meal times
- Lock functionality for finalized plans

### Admin
- Administrative user accounts
- Enhanced permissions for content management

## Security Features

- JWT-based authentication
- Password hashing with bcryptjs
- Rate limiting for API endpoints
- CORS configuration for cross-origin requests
- Input validation and sanitization
- Environment-based configuration

## Monitoring and Analytics

- API call tracking and analytics
- User activity monitoring
- Error logging and handling
- Performance metrics collection

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the ISC License.
