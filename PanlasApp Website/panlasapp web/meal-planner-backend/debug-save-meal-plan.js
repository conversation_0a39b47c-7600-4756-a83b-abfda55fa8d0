const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

// Test data that matches the frontend structure
const testMealPlanData = {
  name: "Test Meal Plan for 2025-01-31",
  startDate: "2025-01-31",
  endDate: "2025-01-31",
  dietaryPreference: "all",
  riceBowls: 0,
  riceBowlsPerDay: { "2025-01-31": 0 },
  meals: [
    {
      date: "2025-01-31",
      mealType: "breakfast",
      mealData: {
        _id: "test-meal-id-1",
        name: "Test Breakfast Meal",
        calories: 300,
        category: ["Breakfast"],
        description: "A test breakfast meal",
        image: "",
        ingredients: ["Test ingredient 1", "Test ingredient 2"],
        instructions: ["Test instruction 1", "Test instruction 2"],
        dietaryTags: [],
        rating: 4,
        instanceId: "test-breakfast-1643723400000"
      }
    },
    {
      date: "2025-01-31",
      mealType: "lunch",
      mealData: {
        _id: "test-meal-id-2",
        name: "Test Lunch Meal",
        calories: 500,
        category: ["Lunch"],
        description: "A test lunch meal",
        image: "",
        ingredients: ["Test ingredient 3", "Test ingredient 4"],
        instructions: ["Test instruction 3", "Test instruction 4"],
        dietaryTags: [],
        rating: 5,
        instanceId: "test-lunch-1643723400000"
      }
    }
  ],
  mealTimes: {
    breakfast: "08:00",
    lunch: "12:00",
    dinner: "18:00",
    snack: "15:00"
  }
};

async function testSaveMealPlan() {
  try {
    console.log('🧪 Testing Save Meal Plan API...');
    
    // First, try to login to get a token
    console.log('Step 1: Logging in...');
    const loginResponse = await axios.post(`${API_BASE_URL}/users/login`, {
      email: '<EMAIL>', // Replace with a valid test user
      password: 'password123'     // Replace with the correct password
    });

    const token = loginResponse.data.token;
    console.log('✅ Login successful, token received:', token ? 'Yes' : 'No');

    if (!token) {
      throw new Error('No token received from login');
    }

    // Now try to save the meal plan
    console.log('Step 2: Attempting to save meal plan...');
    console.log('📤 Sending data:', JSON.stringify(testMealPlanData, null, 2));

    const mealPlanResponse = await axios.post(`${API_BASE_URL}/meal-plans/save`, testMealPlanData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Meal plan save successful!');
    console.log('📥 Response:', JSON.stringify(mealPlanResponse.data, null, 2));

  } catch (error) {
    console.error('❌ Test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Status Text:', error.response.statusText);
      console.error('Headers:', error.response.headers);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run the test
testSaveMealPlan();
