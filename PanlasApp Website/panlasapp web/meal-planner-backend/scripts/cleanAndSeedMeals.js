const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const Meal = require('../models/Meal');
require('dotenv').config();

// Function to read the JSON meals data
const readMealsData = () => {
  try {
    const filePath = '/Users/<USER>/Documents/PanlasApp-Complete/JSON_meals.txt';
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading meals data:', error);
    return [];
  }
};

// Function to map image paths from Food_Images_checked directory
const mapImagePath = (mealName, originalImagePath) => {
  const imageDir = '/Users/<USER>/Documents/PanlasApp-Complete/Food_Images_checked/Food Images';
  
  // Extract filename from original path
  let imageName = '';
  if (originalImagePath && originalImagePath.includes('/')) {
    imageName = originalImagePath.split('/').pop();
  }
  
  // Check if image exists in the Food Images directory
  const imagePath = path.join(imageDir, imageName);
  if (fs.existsSync(imagePath)) {
    return `/Food Images/${imageName}`;
  }
  
  // Try to find image by meal name matching
  const files = fs.readdirSync(imageDir);
  const mealNameLower = mealName.toLowerCase()
    .replace(/[^a-z0-9]/g, '_')
    .replace(/_+/g, '_')
    .replace(/^_|_$/g, '');
  
  // Look for exact matches or close matches
  const possibleMatches = files.filter(file => {
    const fileName = file.toLowerCase().replace('.jpg', '').replace('.png', '');
    return fileName.includes(mealNameLower) || mealNameLower.includes(fileName);
  });
  
  if (possibleMatches.length > 0) {
    return `/Food Images/${possibleMatches[0]}`;
  }
  
  // Return original path if no match found
  return originalImagePath || '/Food Images/default_filipino.jpg';
};

// Function to ensure all required fields are present and properly formatted
const processMealData = (meal) => {
  // Ensure category is an array
  let category = meal.category;
  if (typeof category === 'string') {
    category = [category];
  }
  
  // Ensure mealType is an array
  let mealType = meal.mealType || [];
  if (typeof mealType === 'string') {
    mealType = [mealType];
  }
  
  // Process image path
  const imagePath = mapImagePath(meal.name, meal.image);
  
  // Ensure all required fields have default values
  const processedMeal = {
    name: meal.name || 'Unknown Meal',
    description: meal.description || '',
    calories: meal.calories || 0,
    protein: meal.protein || 0,
    carbs: meal.carbs || 0,
    fat: meal.fat || 0,
    category: category,
    mealType: mealType,
    image: imagePath,
    ingredients: meal.ingredients || [],
    instructions: meal.instructions || [],
    dietaryTags: meal.dietaryTags || [],
    allergens: meal.allergens || [],
    rating: meal.rating || 0,
    prepTime: meal.prepTime || 0,
    price: meal.price || 0,
    region: 'Philippines',
    servingSize: 4,
    
    // Ensure dietType object has all required boolean fields
    dietType: {
      isVegetarian: meal.dietType?.isVegetarian || false,
      isVegan: meal.dietType?.isVegan || false,
      isGlutenFree: meal.dietType?.isGlutenFree || false,
      isDairyFree: meal.dietType?.isDairyFree || false,
      isNutFree: meal.dietType?.isNutFree || false,
      isLowCarb: meal.dietType?.isLowCarb || false,
      isKeto: meal.dietType?.isKeto || false,
      isPescatarian: meal.dietType?.isPescatarian || false,
      isHalal: meal.dietType?.isHalal || false
    },
    
    // Keep dietaryAttributes for backward compatibility
    dietaryAttributes: {
      isVegetarian: meal.dietType?.isVegetarian || false,
      isVegan: meal.dietType?.isVegan || false,
      isGlutenFree: meal.dietType?.isGlutenFree || false,
      isDairyFree: meal.dietType?.isDairyFree || false,
      isNutFree: meal.dietType?.isNutFree || false,
      isLowCarb: meal.dietType?.isLowCarb || false
    }
  };
  
  return processedMeal;
};

// Main function to clean and seed meals
const cleanAndSeedMeals = async () => {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Check current meal count
    const currentMealCount = await Meal.countDocuments();
    console.log(`📊 Current meals in database: ${currentMealCount}`);

    if (currentMealCount > 0) {
      console.log('🗑️  Clearing existing meals...');
      const deleteResult = await Meal.deleteMany({});
      console.log(`✅ Deleted ${deleteResult.deletedCount} existing meals`);
    }

    // Read the meals data
    console.log('📖 Reading meals data from JSON file...');
    const mealsData = readMealsData();
    
    if (mealsData.length === 0) {
      console.log('❌ No meal data found to seed.');
      return;
    }

    console.log(`📝 Found ${mealsData.length} meals to process`);

    // Process and prepare meals for insertion
    console.log('⚙️  Processing meal data...');
    const processedMeals = mealsData.map(processMealData);

    // Remove duplicates by name (keep the first occurrence)
    const uniqueMeals = [];
    const seenNames = new Set();
    
    for (const meal of processedMeals) {
      if (!seenNames.has(meal.name)) {
        uniqueMeals.push(meal);
        seenNames.add(meal.name);
      } else {
        console.log(`⚠️  Skipping duplicate meal: ${meal.name}`);
      }
    }

    console.log(`📊 Processed ${uniqueMeals.length} unique meals (removed ${processedMeals.length - uniqueMeals.length} duplicates)`);

    // Insert meals into the database
    console.log('💾 Inserting meals into database...');
    const result = await Meal.insertMany(uniqueMeals);
    console.log(`✅ Successfully seeded ${result.length} meals to the database`);

    // Verify the insertion
    const finalMealCount = await Meal.countDocuments();
    console.log(`📊 Final meal count in database: ${finalMealCount}`);

    // Show some sample meals
    console.log('📋 Sample meals added:');
    const sampleMeals = await Meal.find().limit(5).select('name category mealType dietType.isVegetarian allergens');
    sampleMeals.forEach(meal => {
      console.log(`- ${meal.name} (${meal.category.join(', ')}) - ${meal.mealType.join(', ')} - Vegetarian: ${meal.dietType.isVegetarian}`);
    });

  } catch (error) {
    console.error('❌ Error cleaning and seeding meals:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
};

// Run the script
if (require.main === module) {
  cleanAndSeedMeals();
}

module.exports = { cleanAndSeedMeals };
