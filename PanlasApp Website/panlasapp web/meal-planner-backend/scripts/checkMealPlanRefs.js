const mongoose = require('mongoose');
const User = require('../models/User');
const MealPlan = require('../models/MealPlan');

// MongoDB connection string
const MONGODB_URI = 'mongodb+srv://johnmatthewmartin09:<EMAIL>/mealplanner?retryWrites=true&w=majority&appName=MealPlannersCluster';

async function checkMealPlanRefs() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB successfully');

    // Find the specific user who is having issues
    const user = await User.findOne({ email: '<EMAIL>' })
      .select('favoriteMealPlans')
      .populate('favoriteMealPlans.plan');
    
    if (!user) {
      console.log('User not found');
      return;
    }

    console.log(`\nUser: ${user.email}`);
    console.log(`Favorite meal plans count: ${user.favoriteMealPlans ? user.favoriteMealPlans.length : 0}`);
    
    if (user.favoriteMealPlans && user.favoriteMealPlans.length > 0) {
      console.log('\nFavorite meal plans with population:');
      for (let i = 0; i < user.favoriteMealPlans.length; i++) {
        const plan = user.favoriteMealPlans[i];
        console.log(`${i + 1}. Plan ID: "${plan.plan}" (type: ${typeof plan.plan})`);
        console.log(`   Name: "${plan.name}"`);
        console.log(`   Date: "${plan.date}"`);
        console.log(`   Populated plan exists: ${plan.plan !== null}`);
        
        if (plan.plan) {
          console.log(`   Populated plan name: "${plan.plan.name}"`);
          console.log(`   Populated plan date: "${plan.plan.date}"`);
        } else {
          console.log(`   ❌ Referenced meal plan does not exist in database!`);
          
          // Check if the meal plan ID exists in the MealPlan collection
          const mealPlanExists = await MealPlan.findById(plan.plan);
          console.log(`   Meal plan exists in collection: ${mealPlanExists !== null}`);
        }
        console.log('---');
      }
    }

    // Also check without population to see raw data
    console.log('\n=== Raw data without population ===');
    const userRaw = await User.findOne({ email: '<EMAIL>' })
      .select('favoriteMealPlans');
    
    if (userRaw.favoriteMealPlans && userRaw.favoriteMealPlans.length > 0) {
      userRaw.favoriteMealPlans.forEach((plan, index) => {
        console.log(`${index + 1}. Raw Plan ID: "${plan.plan}"`);
        console.log(`   Raw Name: "${plan.name}"`);
        console.log(`   Raw Date: "${plan.date}"`);
        console.log('---');
      });
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the check
checkMealPlanRefs();
