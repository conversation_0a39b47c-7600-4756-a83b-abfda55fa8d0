const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

// Test configuration
const tests = [
  {
    name: 'Health Check',
    method: 'GET',
    url: `${BASE_URL}/`,
    requiresAuth: false
  },
  {
    name: 'CORS Test',
    method: 'GET',
    url: `${BASE_URL}/api/cors-test`,
    requiresAuth: false
  },
  {
    name: 'Activity Debug Test',
    method: 'GET',
    url: `${BASE_URL}/api/debug/activity-test`,
    requiresAuth: false
  },
  {
    name: 'Activity Ping',
    method: 'GET',
    url: `${BASE_URL}/api/activity/ping`,
    requiresAuth: false
  },
  {
    name: 'Activity Simple Test',
    method: 'POST',
    url: `${BASE_URL}/api/activity/test-simple`,
    requiresAuth: false,
    data: { test: 'data' }
  }
];

// Function to run tests
async function runTests() {
  console.log('🧪 Starting API endpoint tests...\n');
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    try {
      console.log(`🔄 Testing: ${test.name}`);
      console.log(`   ${test.method} ${test.url}`);
      
      const config = {
        method: test.method.toLowerCase(),
        url: test.url,
        timeout: 5000
      };
      
      if (test.data) {
        config.data = test.data;
        config.headers = { 'Content-Type': 'application/json' };
      }
      
      const response = await axios(config);
      
      console.log(`   ✅ Status: ${response.status}`);
      console.log(`   📄 Response: ${JSON.stringify(response.data).substring(0, 100)}...`);
      console.log('');
      
      passedTests++;
      
    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
      if (error.response) {
        console.log(`   📄 Status: ${error.response.status}`);
        console.log(`   📄 Response: ${JSON.stringify(error.response.data)}`);
      }
      console.log('');
    }
  }
  
  console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed\n`);
  
  if (passedTests === totalTests) {
    console.log('✅ All basic tests passed! The API endpoints are working correctly.');
    console.log('🔧 If you\'re still getting 404 errors in the frontend, the issue might be:');
    console.log('   1. Authentication token format');
    console.log('   2. CORS configuration');
    console.log('   3. Frontend URL construction');
    console.log('\n🎯 Next steps:');
    console.log('   1. Test with authentication token');
    console.log('   2. Check browser network tab for exact request details');
    console.log('   3. Verify frontend is calling the correct URLs');
  } else {
    console.log('❌ Some tests failed. Check the backend server:');
    console.log('   1. Make sure the server is running on port 5000');
    console.log('   2. Check for any startup errors');
    console.log('   3. Verify all route files are properly loaded');
  }
}

// Function to test with authentication
async function testWithAuth(token) {
  console.log('\n🔐 Testing authenticated endpoints...\n');
  
  const authTests = [
    {
      name: 'Database Status',
      method: 'GET',
      url: `${BASE_URL}/api/activity/status`
    },
    {
      name: 'Manual Activity Creation',
      method: 'POST',
      url: `${BASE_URL}/api/activity/manual`
    },
    {
      name: 'Test Activities Creation',
      method: 'POST',
      url: `${BASE_URL}/api/activity/test`
    },
    {
      name: 'Activity Logs',
      method: 'GET',
      url: `${BASE_URL}/api/activity/log`
    }
  ];
  
  let passedAuthTests = 0;
  
  for (const test of authTests) {
    try {
      console.log(`🔄 Testing: ${test.name}`);
      console.log(`   ${test.method} ${test.url}`);
      
      const config = {
        method: test.method.toLowerCase(),
        url: test.url,
        headers: {
          'x-auth-token': token,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      };
      
      const response = await axios(config);
      
      console.log(`   ✅ Status: ${response.status}`);
      console.log(`   📄 Response: ${JSON.stringify(response.data).substring(0, 150)}...`);
      console.log('');
      
      passedAuthTests++;
      
    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
      if (error.response) {
        console.log(`   📄 Status: ${error.response.status}`);
        console.log(`   📄 Response: ${JSON.stringify(error.response.data)}`);
      }
      console.log('');
    }
  }
  
  console.log(`📊 Auth Test Results: ${passedAuthTests}/${authTests.length} tests passed\n`);
  
  if (passedAuthTests === authTests.length) {
    console.log('✅ All authenticated tests passed! The activity logging system is working correctly.');
  } else {
    console.log('❌ Some authenticated tests failed. Check:');
    console.log('   1. Token validity');
    console.log('   2. User permissions (admin required for some endpoints)');
    console.log('   3. Database connection');
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const token = args[0];
  
  console.log('🚀 API Endpoint Debugging Tool');
  console.log('================================\n');
  
  // Run basic tests first
  await runTests();
  
  // If token provided, run auth tests
  if (token) {
    await testWithAuth(token);
  } else {
    console.log('💡 To test authenticated endpoints, run:');
    console.log('   node scripts/debugApiEndpoints.js YOUR_AUTH_TOKEN');
    console.log('\n🔑 Get your token from browser console:');
    console.log('   localStorage.getItem(\'token\')');
  }
  
  console.log('\n🏁 Testing completed!');
}

// Run the tests
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Test runner failed:', error.message);
    process.exit(1);
  });
}

module.exports = { runTests, testWithAuth };
