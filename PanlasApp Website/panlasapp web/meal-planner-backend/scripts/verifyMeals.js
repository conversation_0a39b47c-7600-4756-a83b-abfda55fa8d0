const mongoose = require('mongoose');
const Meal = require('../models/Meal');
require('dotenv').config();

async function verifyMeals() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
    
    // Get total count
    const totalCount = await Meal.countDocuments();
    console.log(`📊 Total meals in database: ${totalCount}`);
    
    // Get a few sample meals to verify the data structure
    const meals = await Meal.find().limit(3);
    
    console.log('\n📋 Sample meal data verification:');
    meals.forEach((meal, index) => {
      console.log(`\n--- Meal ${index + 1}: ${meal.name} ---`);
      console.log(`Category: ${meal.category}`);
      console.log(`MealType: ${meal.mealType}`);
      console.log(`Calories: ${meal.calories}`);
      console.log(`Image: ${meal.image}`);
      console.log(`Allergens: ${meal.allergens}`);
      console.log(`DietType - Vegetarian: ${meal.dietType.isVegetarian}, Vegan: ${meal.dietType.isVegan}, Halal: ${meal.dietType.isHalal}`);
      console.log(`Ingredients count: ${meal.ingredients.length}`);
      console.log(`Instructions count: ${meal.instructions.length}`);
    });
    
    // Check for meals with images
    const mealsWithImages = await Meal.countDocuments({ image: { $exists: true, $ne: '' } });
    console.log(`\n🖼️  Meals with images: ${mealsWithImages}/${totalCount}`);
    
    // Check dietary types distribution
    const vegetarianCount = await Meal.countDocuments({ 'dietType.isVegetarian': true });
    const veganCount = await Meal.countDocuments({ 'dietType.isVegan': true });
    const halalCount = await Meal.countDocuments({ 'dietType.isHalal': true });
    
    console.log(`\n🥗 Dietary distribution:`);
    console.log(`- Vegetarian: ${vegetarianCount}`);
    console.log(`- Vegan: ${veganCount}`);
    console.log(`- Halal: ${halalCount}`);
    
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    
  } catch (error) {
    console.error('❌ Error verifying meals:', error);
  }
}

verifyMeals();
