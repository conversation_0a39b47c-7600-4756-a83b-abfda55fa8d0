const geminiService = require('../services/geminiService');
require('dotenv').config();

async function testGeminiAI() {
  console.log('🧪 Testing Gemini AI Service...\n');

  try {
    // Test 1: Basic connection
    console.log('1. Testing basic connection...');
    const basicTest = await geminiService.generateContent('Say hello in one sentence.');
    console.log('✅ Basic connection:', basicTest);

    // Test 2: Dietary conflict detection
    console.log('\n2. Testing dietary conflict detection...');
    const conflictTest = await geminiService.detectDietaryConflicts({
      restrictions: ['Keto', 'Vegan', 'High-Carb'],
      allergies: ['Nuts', 'Dairy'],
      dislikedIngredients: ['Mushrooms', 'Onions']
    });
    console.log('✅ Conflict detection result:');
    console.log(JSON.stringify(conflictTest, null, 2));

    // Test 3: Goal-based suggestions
    console.log('\n3. Testing goal-based suggestions...');
    const goalTest = await geminiService.generateGoalBasedSuggestions('Lose Weight');
    console.log('✅ Goal suggestions result:');
    console.log(JSON.stringify(goalTest, null, 2));

    // Test 4: Health condition suggestions
    console.log('\n4. Testing health condition suggestions...');
    const healthTest = await geminiService.generateGoalBasedSuggestions('Manage a Health Condition', 'Type 2 Diabetes');
    console.log('✅ Health condition suggestions result:');
    console.log(JSON.stringify(healthTest, null, 2));

    // Test 5: Chat response
    console.log('\n5. Testing chat response...');
    const chatTest = await geminiService.generateChatResponse('What are some healthy Filipino breakfast options?');
    console.log('✅ Chat response:', chatTest);

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testGeminiAI();
