const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

// Test data - same as the user's payload
const testMealPlan = {
  "name": "test",
  "startDate": "2025-05-28",
  "endDate": "2025-05-28",
  "dietaryPreference": "all",
  "meals": [
    {
      "date": "2025-05-28",
      "mealType": "breakfast",
      "meal": 19,
      "mealData": {
        "name": "Dinuguan",
        "calories": 420,
        "category": "Stew",
        "ingredients": [
          "2 lbs pork shoulder, cubed",
          "1/2 lb pork offal (heart, liver, intestines), cleaned and sliced"
        ],
        "instructions": [
          "Heat oil in a pot. Sauté garlic, onions, and ginger until fragrant.",
          "Add pork and offal. Cook until browned."
        ]
      }
    }
  ],
  "mealTimes": {
    "breakfast": "08:00",
    "lunch": "12:00",
    "dinner": "18:00",
    "snack": "15:00"
  }
};

async function testMealPlanSave() {
  try {
    console.log('Testing meal plan save...');

    // First, let's create a test user
    console.log('Step 1: Creating test user...');

    const testUser = {
      username: 'testuser' + Date.now(),
      email: 'test' + Date.now() + '@example.com',
      password: 'testpassword123',
      firstName: 'Test',
      lastName: 'User'
    };

    try {
      const signupResponse = await axios.post(`${API_BASE_URL}/users/signup`, testUser);
      console.log('Test user created successfully!');
    } catch (signupError) {
      console.log('User creation failed, trying to login with existing user...');
    }

    // Now try to login
    console.log('Step 2: Attempting to login...');

    // Try with different users from the database
    const users = [
      { email: testUser.email, password: testUser.password },
      { email: '<EMAIL>', password: 'password123' },
      { email: '<EMAIL>', password: 'password123' },
      { email: '<EMAIL>', password: '132fast@!' },
      { email: '<EMAIL>', password: 'password123' }
    ];

    let loginResponse = null;
    let token = null;

    for (const user of users) {
      try {
        console.log(`Trying to login with: ${user.email}`);
        loginResponse = await axios.post(`${API_BASE_URL}/users/login`, user);
        token = loginResponse.data.token;
        console.log(`Login successful with: ${user.email}`);
        break;
      } catch (loginError) {
        console.log(`Login failed for ${user.email}: ${loginError.response?.data?.message || loginError.message}`);
      }
    }

    if (!token) {
      throw new Error('Could not login with any test user');
    }

    console.log('Token received:', token ? 'Yes' : 'No');

    // Now try to save the meal plan
    console.log('Step 2: Attempting to save meal plan...');

    const mealPlanResponse = await axios.post(`${API_BASE_URL}/meal-plans/save`, testMealPlan, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('Meal plan save successful!');
    console.log('Response:', mealPlanResponse.data);

  } catch (error) {
    console.error('Test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run the test
testMealPlanSave();
