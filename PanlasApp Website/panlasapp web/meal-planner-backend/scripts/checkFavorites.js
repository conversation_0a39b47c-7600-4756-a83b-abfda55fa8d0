const mongoose = require('mongoose');
const User = require('../models/User');

// MongoDB connection string
const MONGODB_URI = 'mongodb+srv://johnmatthewmartin09:<EMAIL>/mealplanner?retryWrites=true&w=majority&appName=MealPlannersCluster';

async function checkFavorites() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB successfully');

    // Find the specific user who is having issues
    const user = await User.findOne({ email: '<EMAIL>' });
    
    if (!user) {
      console.log('User not found');
      return;
    }

    console.log(`\nUser: ${user.email}`);
    console.log(`Favorite meal plans count: ${user.favoriteMealPlans ? user.favoriteMealPlans.length : 0}`);
    
    if (user.favoriteMealPlans && user.favoriteMealPlans.length > 0) {
      console.log('\nFavorite meal plans:');
      user.favoriteMealPlans.forEach((plan, index) => {
        console.log(`${index + 1}. Plan ID: "${plan.plan}" (type: ${typeof plan.plan})`);
        console.log(`   Name: "${plan.name}"`);
        console.log(`   Date: "${plan.date}"`);
        console.log(`   Valid ObjectId: ${mongoose.Types.ObjectId.isValid(plan.plan)}`);
        console.log('---');
      });
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the check
checkFavorites();
