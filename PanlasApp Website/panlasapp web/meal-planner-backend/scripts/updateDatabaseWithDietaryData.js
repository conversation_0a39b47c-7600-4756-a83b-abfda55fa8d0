const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Import the Meal model
const Meal = require('../models/Meal');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Function to update database with complete dietary and allergen data
const updateDatabaseWithDietaryData = async () => {
  try {
    console.log('🔄 Starting comprehensive database update...');
    
    // Read the Filipino meals data
    const filePath = path.join(__dirname, 'filipinoMealData.json');
    const mealsData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
    console.log(`📊 Found ${mealsData.length} meals in data file`);

    let addedCount = 0;
    let updatedCount = 0;
    let errorCount = 0;

    for (const mealData of mealsData) {
      try {
        // Check if meal already exists
        const existingMeal = await Meal.findOne({ name: mealData.name });
        
        // Ensure allergens are included
        if (!mealData.allergens) {
          console.log(`⚠️  No allergens found for ${mealData.name}, adding empty array`);
          mealData.allergens = [];
        }

        // Prepare meal object with full dietary and allergen data
        const mealObject = {
          ...mealData,
          dietaryAttributes: mealData.dietType, // Keep for backward compatibility
          allergens: mealData.allergens, // Explicitly include allergens
          image: `http://*************:5000${mealData.image}` // Add full URL
        };

        if (existingMeal) {
          // Update existing meal with new dietary and allergen information
          await Meal.findByIdAndUpdate(existingMeal._id, {
            $set: mealObject
          });
          updatedCount++;
          
          if (mealData.dietType?.isVegan) {
            console.log(`🌿 Updated vegan meal: "${mealData.name}"`);
          }
        } else {
          // Add new meal
          const newMeal = new Meal(mealObject);
          await newMeal.save();
          addedCount++;
          
          if (mealData.dietType?.isVegan) {
            console.log(`🌿 Added new vegan meal: "${mealData.name}"`);
          }
        }

      } catch (error) {
        console.error(`❌ Error processing meal "${mealData.name}":`, error.message);
        errorCount++;
      }
    }

    console.log('\n📈 Update Summary:');
    console.log(`✅ Successfully added: ${addedCount} meals`);
    console.log(`🔄 Successfully updated: ${updatedCount} meals`);
    console.log(`❌ Errors: ${errorCount} meals`);

    // Verify dietary data in database
    const veganMeals = await Meal.find({ 'dietType.isVegan': true });
    const vegetarianMeals = await Meal.find({ 'dietType.isVegetarian': true });
    const glutenFreeMeals = await Meal.find({ 'dietType.isGlutenFree': true });
    const nutFreeMeals = await Meal.find({ 'dietType.isNutFree': true });
    
    console.log('\n🏷️  Database Dietary Statistics:');
    console.log(`🌿 Vegan meals: ${veganMeals.length}`);
    console.log(`🌱 Vegetarian meals: ${vegetarianMeals.length}`);
    console.log(`🌾 Gluten-Free meals: ${glutenFreeMeals.length}`);
    console.log(`🥜 Nut-Free meals: ${nutFreeMeals.length}`);
    
    console.log('\n🌿 Vegan meals in database:');
    veganMeals.forEach(meal => {
      console.log(`  - ${meal.name} (${meal.calories || 0} cal)`);
    });

    // Verify allergen data
    const mealsWithAllergens = await Meal.find({ allergens: { $exists: true, $ne: [] } });
    console.log(`\n⚠️  Meals with allergen data: ${mealsWithAllergens.length}`);

    console.log('\n🎉 Database update completed successfully!');

  } catch (error) {
    console.error('❌ Error in updateDatabaseWithDietaryData:', error);
  }
};

// Main execution function
const main = async () => {
  await connectDB();
  await updateDatabaseWithDietaryData();
  
  console.log('\n🏁 Script completed successfully!');
  process.exit(0);
};

// Run the script
main().catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
