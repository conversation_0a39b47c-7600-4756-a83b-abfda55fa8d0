const geminiService = require('../services/geminiService');
require('dotenv').config();

async function testGoalBasedRecommendations() {
  console.log('🧪 Testing Goal-based Recommendations...\n');

  try {
    // Test 1: Lose Weight goal
    console.log('1. Testing "Lose Weight" goal...');
    const loseWeightSuggestions = await geminiService.generateGoalBasedSuggestions('Lose Weight');
    console.log('✅ Lose Weight Suggestions:');
    console.log(JSON.stringify(loseWeightSuggestions, null, 2));

    // Test 2: Build Muscle goal
    console.log('\n2. Testing "Build Muscle" goal...');
    const buildMuscleSuggestions = await geminiService.generateGoalBasedSuggestions('Build Muscle');
    console.log('✅ Build Muscle Suggestions:');
    console.log(JSON.stringify(buildMuscleSuggestions, null, 2));

    // Test 3: Eat Sustainably goal
    console.log('\n3. Testing "Eat Sustainably" goal...');
    const sustainableSuggestions = await geminiService.generateGoalBasedSuggestions('Eat Sustainably');
    console.log('✅ Eat Sustainably Suggestions:');
    console.log(JSON.stringify(sustainableSuggestions, null, 2));

    // Test 4: Manage Health Condition with Type 2 Diabetes
    console.log('\n4. Testing "Manage Health Condition" with Type 2 Diabetes...');
    const diabetesSuggestions = await geminiService.generateGoalBasedSuggestions('Manage a Health Condition', 'Type 2 Diabetes');
    console.log('✅ Type 2 Diabetes Suggestions:');
    console.log(JSON.stringify(diabetesSuggestions, null, 2));

    // Test 5: Manage Health Condition with Celiac Disease
    console.log('\n5. Testing "Manage Health Condition" with Celiac Disease...');
    const celiacSuggestions = await geminiService.generateGoalBasedSuggestions('Manage a Health Condition', 'Celiac Disease');
    console.log('✅ Celiac Disease Suggestions:');
    console.log(JSON.stringify(celiacSuggestions, null, 2));

    // Test 6: Manage Health Condition with Hypertension
    console.log('\n6. Testing "Manage Health Condition" with Hypertension...');
    const hypertensionSuggestions = await geminiService.generateGoalBasedSuggestions('Manage a Health Condition', 'Hypertension');
    console.log('✅ Hypertension Suggestions:');
    console.log(JSON.stringify(hypertensionSuggestions, null, 2));

    console.log('\n🎉 All goal-based recommendation tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testGoalBasedRecommendations();
