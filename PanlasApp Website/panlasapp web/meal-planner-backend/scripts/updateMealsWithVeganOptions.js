const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Import the Meal model
const Meal = require('../models/Meal');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Import the allergen analysis function
const { analyzeAllergens } = require('./addAllergensToMeals');

// Function to analyze meal and determine dietary restrictions
const analyzeMealDietaryRestrictions = (meal) => {
  const dietType = {
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false,
    isDairyFree: false,
    isNutFree: false,
    isLowCarb: false,
    isKeto: false,
    isPescatarian: false,
    isHalal: false
  };

  // Convert ingredients to lowercase for easier checking
  const ingredients = meal.ingredients ? meal.ingredients.join(' ').toLowerCase() : '';
  const name = meal.name.toLowerCase();
  const description = meal.description ? meal.description.toLowerCase() : '';
  const category = meal.category ? (Array.isArray(meal.category) ? meal.category.join(' ') : meal.category).toLowerCase() : '';
  const dietaryTags = meal.dietaryTags ? meal.dietaryTags.join(' ').toLowerCase() : '';

  // Check dietary tags first (most reliable)
  if (dietaryTags.includes('vegan')) {
    dietType.isVegan = true;
    dietType.isVegetarian = true; // Vegan is also vegetarian
  } else if (dietaryTags.includes('vegetarian')) {
    dietType.isVegetarian = true;
  }

  if (dietaryTags.includes('gluten-free')) {
    dietType.isGlutenFree = true;
  }

  if (dietaryTags.includes('low-carb')) {
    dietType.isLowCarb = true;
  }

  // Use allergen analysis to determine dietary restrictions
  const allergens = meal.allergens || analyzeAllergens(meal);

  // Set dietary flags based on allergens
  dietType.isGlutenFree = !allergens.includes('gluten');
  dietType.isDairyFree = !allergens.includes('dairy');
  dietType.isNutFree = !allergens.includes('nuts');

  // Meat indicators
  const meatKeywords = [
    'chicken', 'pork', 'beef', 'meat', 'manok', 'baboy', 'baka', 'lechon', 'tapa', 'tocino',
    'longganisa', 'sisig', 'dinuguan', 'menudo', 'caldereta', 'afritada', 'inasal',
    'nilaga', 'tinola', 'oxtail', 'tripe'
  ];

  // Seafood indicators
  const seafoodKeywords = [
    'bangus', 'tilapia', 'tuna', 'salmon', 'isda', 'hipon', 'pusit', 'alimango',
    'daing', 'dried fish', 'tuyo', 'shrimp', 'crab', 'squid'
  ];

  // Check for meat (only if not already marked as vegan/vegetarian from tags)
  if (!dietType.isVegan && !dietType.isVegetarian) {
    const hasMeat = meatKeywords.some(keyword =>
      ingredients.includes(keyword) || name.includes(keyword)
    );

    // Check for seafood (but exclude fish sauce which is just seasoning)
    const hasSeafood = seafoodKeywords.some(keyword =>
      ingredients.includes(keyword) || name.includes(keyword)
    );

    // Check for fish sauce separately (doesn't make it pescatarian, just not vegan)
    const hasFishSauce = allergens.includes('fish');

    if (!hasMeat && !hasSeafood) {
      dietType.isVegetarian = true;

      // Check if it's also vegan (no dairy, eggs, or fish sauce)
      const hasDairy = allergens.includes('dairy');
      const hasEggs = allergens.includes('eggs');

      if (!hasDairy && !hasEggs && !hasFishSauce) {
        dietType.isVegan = true;
      }
    } else if (!hasMeat && hasSeafood) {
      dietType.isPescatarian = true;
    }
  }

  // Low carb check (less than 20g carbs)
  dietType.isLowCarb = meal.carbs && meal.carbs < 20;
  
  // Keto check (very low carb, high fat)
  dietType.isKeto = meal.carbs && meal.fat && meal.carbs < 10 && meal.fat > 15;

  // Halal check (no pork, no alcohol)
  const porkKeywords = ['pork', 'baboy', 'lechon', 'tocino', 'longganisa', 'sisig'];
  const hasPork = porkKeywords.some(keyword => 
    ingredients.includes(keyword) || name.includes(keyword) || description.includes(keyword)
  );
  dietType.isHalal = !hasPork;

  return dietType;
};

// Function to update database with new meals
const updateMealsDatabase = async () => {
  try {
    console.log('🔄 Starting meals database update...');
    
    // Read the Filipino meals data
    const filePath = path.join(__dirname, 'filipinoMealData.json');
    const mealsData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
    console.log(`📊 Found ${mealsData.length} meals in data file`);

    let addedCount = 0;
    let updatedCount = 0;
    let errorCount = 0;

    for (const mealData of mealsData) {
      try {
        // Check if meal already exists
        const existingMeal = await Meal.findOne({ name: mealData.name });
        
        // Add allergen information if not present
        if (!mealData.allergens) {
          mealData.allergens = analyzeAllergens(mealData);
        }

        // Analyze dietary restrictions
        const dietType = analyzeMealDietaryRestrictions(mealData);

        // Prepare meal object
        const mealObject = {
          ...mealData,
          dietType: dietType,
          dietaryAttributes: dietType, // Keep for backward compatibility
          allergens: mealData.allergens,
          image: `http://*************:5000${mealData.image}` // Add full URL
        };

        if (existingMeal) {
          // Update existing meal with new dietary information
          await Meal.findByIdAndUpdate(existingMeal._id, {
            $set: {
              dietType: dietType,
              dietaryAttributes: dietType,
              ...mealData
            }
          });
          updatedCount++;
          
          if (dietType.isVegan) {
            console.log(`🌿 Updated vegan meal: "${mealData.name}"`);
          }
        } else {
          // Add new meal
          const newMeal = new Meal(mealObject);
          await newMeal.save();
          addedCount++;
          
          if (dietType.isVegan) {
            console.log(`🌿 Added new vegan meal: "${mealData.name}"`);
          }
        }

      } catch (error) {
        console.error(`❌ Error processing meal "${mealData.name}":`, error.message);
        errorCount++;
      }
    }

    console.log('\n📈 Update Summary:');
    console.log(`✅ Successfully added: ${addedCount} meals`);
    console.log(`🔄 Successfully updated: ${updatedCount} meals`);
    console.log(`❌ Errors: ${errorCount} meals`);

    // Verify vegan meals
    const veganMeals = await Meal.find({ 'dietType.isVegan': true });
    const vegetarianMeals = await Meal.find({ 'dietType.isVegetarian': true });
    
    console.log('\n🌿 Dietary Statistics:');
    console.log(`🌿 Total vegan meals: ${veganMeals.length}`);
    console.log(`🌱 Total vegetarian meals: ${vegetarianMeals.length}`);
    
    console.log('\n🌿 Vegan meals in database:');
    veganMeals.forEach(meal => {
      console.log(`  - ${meal.name} (${meal.calories || 0} cal)`);
    });

    console.log('\n🎉 Database update completed successfully!');

  } catch (error) {
    console.error('❌ Error in updateMealsDatabase:', error);
  }
};

// Main execution function
const main = async () => {
  await connectDB();
  await updateMealsDatabase();
  
  console.log('\n🏁 Script completed successfully!');
  process.exit(0);
};

// Run the script
main().catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
