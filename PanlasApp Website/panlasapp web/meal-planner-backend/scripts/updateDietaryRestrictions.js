const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Import the Meal model
const Meal = require('../models/Meal');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Function to analyze meal and determine dietary restrictions
const analyzeMealDietaryRestrictions = (meal) => {
  const dietType = {
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false,
    isDairyFree: false,
    isNutFree: false,
    isLowCarb: false,
    isKeto: false,
    isPescatarian: false,
    isHalal: false
  };

  // Convert ingredients to lowercase for easier checking
  const ingredients = meal.ingredients ? meal.ingredients.join(' ').toLowerCase() : '';
  const name = meal.name.toLowerCase();
  const description = meal.description ? meal.description.toLowerCase() : '';
  const category = meal.category ? (Array.isArray(meal.category) ? meal.category.join(' ') : meal.category).toLowerCase() : '';

  // Meat indicators
  const meatKeywords = [
    'chicken', 'pork', 'beef', 'meat', 'manok', 'baboy', 'baka', 'lechon', 'tapa', 'tocino', 
    'longganisa', 'sisig', 'dinuguan', 'menudo', 'caldereta', 'afritada', 'adobo', 'inasal',
    'nilaga', 'tinola', 'sinigang', 'kare-kare', 'bicol express', 'lumpia shanghai'
  ];

  // Seafood indicators
  const seafoodKeywords = [
    'fish', 'shrimp', 'crab', 'squid', 'bangus', 'tilapia', 'tuna', 'salmon', 'isda', 
    'hipon', 'pusit', 'alimango', 'daing', 'dried fish', 'tuyo'
  ];

  // Dairy indicators
  const dairyKeywords = [
    'milk', 'cheese', 'butter', 'cream', 'condensed milk', 'evaporated milk', 'gatas',
    'kesong puti', 'queso', 'margarine'
  ];

  // Gluten indicators
  const glutenKeywords = [
    'flour', 'bread', 'noodles', 'pasta', 'pancit', 'bihon', 'canton', 'sotanghon',
    'lumpia wrapper', 'soy sauce', 'wheat', 'harina'
  ];

  // Nut indicators
  const nutKeywords = [
    'peanut', 'cashew', 'almond', 'walnut', 'mani', 'peanut butter', 'nuts'
  ];

  // Check for meat
  const hasMeat = meatKeywords.some(keyword => 
    ingredients.includes(keyword) || name.includes(keyword) || description.includes(keyword)
  );

  // Check for seafood
  const hasSeafood = seafoodKeywords.some(keyword => 
    ingredients.includes(keyword) || name.includes(keyword) || description.includes(keyword)
  );

  // Check for dairy
  const hasDairy = dairyKeywords.some(keyword => 
    ingredients.includes(keyword) || name.includes(keyword) || description.includes(keyword)
  );

  // Check for gluten
  const hasGluten = glutenKeywords.some(keyword => 
    ingredients.includes(keyword) || name.includes(keyword) || description.includes(keyword)
  );

  // Check for nuts
  const hasNuts = nutKeywords.some(keyword => 
    ingredients.includes(keyword) || name.includes(keyword) || description.includes(keyword)
  );

  // Determine dietary restrictions
  dietType.isVegetarian = !hasMeat && !hasSeafood;
  dietType.isVegan = !hasMeat && !hasSeafood && !hasDairy;
  dietType.isGlutenFree = !hasGluten;
  dietType.isDairyFree = !hasDairy;
  dietType.isNutFree = !hasNuts;
  dietType.isPescatarian = !hasMeat && hasSeafood;
  
  // Low carb check (less than 20g carbs)
  dietType.isLowCarb = meal.carbs && meal.carbs < 20;
  
  // Keto check (very low carb, high fat)
  dietType.isKeto = meal.carbs && meal.fat && meal.carbs < 10 && meal.fat > 15;

  // Halal check (no pork, no alcohol)
  const porkKeywords = ['pork', 'baboy', 'lechon', 'tocino', 'longganisa', 'sisig'];
  const hasPork = porkKeywords.some(keyword => 
    ingredients.includes(keyword) || name.includes(keyword) || description.includes(keyword)
  );
  dietType.isHalal = !hasPork;

  return dietType;
};

// Function to update all meals with dietary restrictions
const updateMealsDietaryRestrictions = async () => {
  try {
    console.log('🔄 Starting dietary restrictions update...');
    
    // Get all meals from database
    const meals = await Meal.find({});
    console.log(`📊 Found ${meals.length} meals to update`);

    let updatedCount = 0;
    let errorCount = 0;

    for (const meal of meals) {
      try {
        // Analyze meal and get dietary restrictions
        const dietType = analyzeMealDietaryRestrictions(meal);
        
        // Update the meal with dietary restrictions
        await Meal.findByIdAndUpdate(meal._id, {
          $set: {
            dietType: dietType,
            dietaryAttributes: dietType // Also update the existing field for compatibility
          }
        });

        updatedCount++;
        
        // Log some examples
        if (updatedCount <= 5) {
          console.log(`✅ Updated "${meal.name}":`, {
            vegetarian: dietType.isVegetarian,
            vegan: dietType.isVegan,
            glutenFree: dietType.isGlutenFree,
            dairyFree: dietType.isDairyFree,
            lowCarb: dietType.isLowCarb,
            halal: dietType.isHalal
          });
        }

      } catch (error) {
        console.error(`❌ Error updating meal "${meal.name}":`, error.message);
        errorCount++;
      }
    }

    console.log('\n📈 Update Summary:');
    console.log(`✅ Successfully updated: ${updatedCount} meals`);
    console.log(`❌ Errors: ${errorCount} meals`);
    console.log('🎉 Dietary restrictions update completed!');

  } catch (error) {
    console.error('❌ Error in updateMealsDietaryRestrictions:', error);
  }
};

// Function to verify the updates
const verifyUpdates = async () => {
  try {
    console.log('\n🔍 Verifying updates...');
    
    const vegetarianMeals = await Meal.find({ 'dietType.isVegetarian': true });
    const veganMeals = await Meal.find({ 'dietType.isVegan': true });
    const glutenFreeMeals = await Meal.find({ 'dietType.isGlutenFree': true });
    const dairyFreeMeals = await Meal.find({ 'dietType.isDairyFree': true });
    const lowCarbMeals = await Meal.find({ 'dietType.isLowCarb': true });
    const halalMeals = await Meal.find({ 'dietType.isHalal': true });

    console.log('\n📊 Dietary Restrictions Summary:');
    console.log(`🌱 Vegetarian meals: ${vegetarianMeals.length}`);
    console.log(`🌿 Vegan meals: ${veganMeals.length}`);
    console.log(`🌾 Gluten-free meals: ${glutenFreeMeals.length}`);
    console.log(`🥛 Dairy-free meals: ${dairyFreeMeals.length}`);
    console.log(`⚡ Low-carb meals: ${lowCarbMeals.length}`);
    console.log(`🕌 Halal meals: ${halalMeals.length}`);

    // Show some examples
    if (vegetarianMeals.length > 0) {
      console.log('\n🌱 Sample Vegetarian meals:');
      vegetarianMeals.slice(0, 3).forEach(meal => console.log(`  - ${meal.name}`));
    }

    if (veganMeals.length > 0) {
      console.log('\n🌿 Sample Vegan meals:');
      veganMeals.slice(0, 3).forEach(meal => console.log(`  - ${meal.name}`));
    }

  } catch (error) {
    console.error('❌ Error in verification:', error);
  }
};

// Main execution function
const main = async () => {
  await connectDB();
  await updateMealsDietaryRestrictions();
  await verifyUpdates();
  
  console.log('\n🏁 Script completed successfully!');
  process.exit(0);
};

// Run the script
main().catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
