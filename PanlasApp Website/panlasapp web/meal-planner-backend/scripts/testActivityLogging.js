const mongoose = require('mongoose');
const Activity = require('../models/Activity');
const User = require('../models/User');
const ActivityService = require('../services/activityService');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/meal-planner', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

// Test activity logging
const testActivityLogging = async () => {
  try {
    console.log('🧪 Starting activity logging test...');

    // Find a test user (or create one)
    let testUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!testUser) {
      console.log('📝 Creating test user...');
      testUser = new User({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'hashedpassword', // This would be hashed in real scenario
        firstName: 'Test',
        lastName: 'User',
        isEmailVerified: true
      });
      await testUser.save();
      console.log('✅ Test user created');
    }

    console.log('👤 Using test user:', testUser.email);

    // Create mock request object
    const mockReq = {
      headers: {
        'user-agent': 'Test Script/1.0',
        'x-forwarded-for': '127.0.0.1'
      },
      connection: {
        remoteAddress: '127.0.0.1'
      }
    };

    // Test different activity types
    console.log('🔄 Creating test activities...');

    // 1. Login activity
    await ActivityService.logLogin(testUser._id, testUser.email, mockReq);
    console.log('✅ Login activity logged');

    // 2. Profile update activity
    await ActivityService.logProfileUpdate(testUser._id, {
      firstName: 'Updated',
      lastName: 'Name'
    }, mockReq);
    console.log('✅ Profile update activity logged');

    // 3. Meal plan create activity
    await ActivityService.logMealPlanCreate(testUser._id, {
      date: '2024-01-15',
      mealType: 'breakfast',
      mealName: 'Test Breakfast Meal'
    }, mockReq);
    console.log('✅ Meal plan create activity logged');

    // 4. Meal plan update activity
    await ActivityService.logMealPlanUpdate(testUser._id, {
      date: '2024-01-15',
      mealType: 'lunch',
      mealName: 'Test Lunch Meal'
    }, mockReq);
    console.log('✅ Meal plan update activity logged');

    // 5. Meal plan delete activity
    await ActivityService.logMealPlanDelete(testUser._id, '2024-01-14', mockReq);
    console.log('✅ Meal plan delete activity logged');

    // 6. Logout activity
    await ActivityService.logLogout(testUser._id, mockReq);
    console.log('✅ Logout activity logged');

    // Check if activities were created
    const activityCount = await Activity.countDocuments({ user: testUser._id });
    console.log(`📊 Total activities created for test user: ${activityCount}`);

    // Get recent activities
    const recentActivities = await Activity.find({ user: testUser._id })
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('user', 'username email');

    console.log('📋 Recent activities:');
    recentActivities.forEach((activity, index) => {
      console.log(`  ${index + 1}. ${activity.action} - ${activity.createdAt.toISOString()}`);
    });

    // Test the ActivityService.getActivities method
    console.log('🔍 Testing activity retrieval...');
    const result = await ActivityService.getActivities({}, 1, 10);
    console.log(`📊 Retrieved ${result.activities.length} activities (${result.totalActivities} total)`);

    if (result.activities.length > 0) {
      console.log('📋 Sample retrieved activity:');
      console.log(JSON.stringify(result.activities[0], null, 2));
    }

    console.log('✅ Activity logging test completed successfully!');

  } catch (error) {
    console.error('❌ Error during activity logging test:', error);
  }
};

// Main function
const main = async () => {
  await connectDB();
  await testActivityLogging();
  
  console.log('🏁 Test completed. Closing database connection...');
  await mongoose.connection.close();
  console.log('👋 Database connection closed.');
  process.exit(0);
};

// Run the test
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
}

module.exports = { testActivityLogging };
