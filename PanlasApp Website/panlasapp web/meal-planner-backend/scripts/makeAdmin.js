const mongoose = require('mongoose');
require('dotenv').config();

// Import User model
const User = require('../models/User');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/mealplanner')
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => {
    console.error('Could not connect to MongoDB', err);
    process.exit(1);
  });

// Function to make a user an admin
async function makeAdmin(email) {
  try {
    // Find user by email
    const user = await User.findOne({ email });
    
    if (!user) {
      console.error(`User with email ${email} not found`);
      process.exit(1);
    }
    
    // Update user to be an admin
    user.isAdmin = true;
    await user.save();
    
    console.log(`User ${user.username} (${user.email}) is now an admin`);
    process.exit(0);
  } catch (error) {
    console.error('Error making user an admin:', error);
    process.exit(1);
  }
}

// Get email from command line arguments
const email = process.argv[2];

if (!email) {
  console.error('Please provide an email address');
  console.log('Usage: node makeAdmin.js <EMAIL>');
  process.exit(1);
}

// Make the user an admin
makeAdmin(email);
