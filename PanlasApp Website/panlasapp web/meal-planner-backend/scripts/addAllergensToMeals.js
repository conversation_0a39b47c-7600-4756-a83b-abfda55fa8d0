const fs = require('fs');
const path = require('path');

// Function to analyze ingredients and determine allergens
const analyzeAllergens = (meal) => {
  const allergens = [];

  // Convert ingredients to lowercase for easier matching
  const ingredients = meal.ingredients ? meal.ingredients.join(' ').toLowerCase() : '';
  const name = meal.name.toLowerCase();

  // Check if meal is marked as vegan - if so, exclude dairy and eggs
  const isVegan = meal.dietaryTags && meal.dietaryTags.includes('vegan');

  // Define allergen keywords - only check ingredients, not description
  const allergenMap = {
    'nuts': ['peanut', 'peanuts', 'cashew', 'almond', 'walnut', 'nuts', 'mani', 'peanut butter', 'ground peanuts'],
    'gluten': ['flour', 'wheat', 'bread', 'noodles', 'pasta', 'pancit', 'bihon', 'canton', 'sotanghon', 'lumpia wrapper', 'harina', 'egg noodles'],
    'soy': ['soy sauce', 'tofu', 'soybean', 'soya'],
    'dairy': isVegan ? [] : ['milk', 'cheese', 'butter', 'cream', 'condensed milk', 'evaporated milk', 'gatas', 'kesong puti', 'queso', 'margarine'],
    'eggs': isVegan ? [] : ['egg', 'eggs', 'itlog'],
    'fish': ['fish sauce', 'bagoong', 'dried fish', 'tuyo', 'bangus', 'tilapia', 'tuna', 'salmon', 'isda'],
    'shellfish': ['shrimp', 'crab', 'lobster', 'hipon', 'alimango', 'oyster sauce'],
    'sesame': ['sesame', 'tahini', 'sesame oil']
  };

  // Check for each allergen
  Object.keys(allergenMap).forEach(allergen => {
    const keywords = allergenMap[allergen];
    if (keywords.length > 0) {
      const hasAllergen = keywords.some(keyword => ingredients.includes(keyword));
      if (hasAllergen) {
        allergens.push(allergen);
      }
    }
  });

  // Special handling for specific ingredients that might be optional
  if (ingredients.includes('(optional)')) {
    // Remove allergens that are only in optional ingredients
    const optionalPart = ingredients.split('(optional)')[0];
    const requiredAllergens = [];

    Object.keys(allergenMap).forEach(allergen => {
      const keywords = allergenMap[allergen];
      if (keywords.length > 0) {
        const hasAllergenInRequired = keywords.some(keyword => optionalPart.includes(keyword));
        if (hasAllergenInRequired) {
          requiredAllergens.push(allergen);
        }
      }
    });

    return requiredAllergens;
  }

  return allergens;
};

// Function to update meals with allergen information
const addAllergensToMeals = () => {
  try {
    console.log('🔄 Adding allergen information to Filipino meals...');
    
    // Read the current meals data
    const filePath = path.join(__dirname, 'filipinoMealData.json');
    const mealsData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
    console.log(`📊 Processing ${mealsData.length} meals`);

    // Process each meal
    const updatedMeals = mealsData.map(meal => {
      // Analyze allergens if not already present
      if (!meal.allergens) {
        meal.allergens = analyzeAllergens(meal);
      } else {
        // Merge with existing allergens
        const analyzedAllergens = analyzeAllergens(meal);
        meal.allergens = [...new Set([...meal.allergens, ...analyzedAllergens])];
      }

      // Log meals with allergens
      if (meal.allergens.length > 0) {
        console.log(`⚠️  ${meal.name}: ${meal.allergens.join(', ')}`);
      }

      return meal;
    });

    // Write back to file
    fs.writeFileSync(filePath, JSON.stringify(updatedMeals, null, 2));
    
    // Statistics
    const mealsWithAllergens = updatedMeals.filter(meal => meal.allergens.length > 0);
    const allergenStats = {};
    
    updatedMeals.forEach(meal => {
      meal.allergens.forEach(allergen => {
        allergenStats[allergen] = (allergenStats[allergen] || 0) + 1;
      });
    });

    console.log('\n📈 Allergen Statistics:');
    console.log(`✅ Total meals processed: ${updatedMeals.length}`);
    console.log(`⚠️  Meals with allergens: ${mealsWithAllergens.length}`);
    console.log(`🚫 Allergen-free meals: ${updatedMeals.length - mealsWithAllergens.length}`);
    
    console.log('\n🏷️  Allergen breakdown:');
    Object.entries(allergenStats).forEach(([allergen, count]) => {
      console.log(`   ${allergen}: ${count} meals`);
    });

    console.log('\n🎉 Successfully added allergen information to all meals!');
    
    return updatedMeals;

  } catch (error) {
    console.error('❌ Error adding allergens to meals:', error);
    throw error;
  }
};

// Run the script
if (require.main === module) {
  addAllergensToMeals();
}

module.exports = { addAllergensToMeals, analyzeAllergens };
