const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const Meal = require('../models/Meal');
require('dotenv').config();

// Source and destination directories
const sourceDir = '/Users/<USER>/Documents/PanlasApp-Complete/Food_Images_checked/Food Images';
const destDir = path.join(__dirname, '../../public/imagesfood');

// Function to copy images from source to destination
const copyImages = () => {
  console.log('📁 Copying images from Food_Images_checked to public/imagesfood...');
  
  if (!fs.existsSync(sourceDir)) {
    console.error('❌ Source directory does not exist:', sourceDir);
    return false;
  }

  if (!fs.existsSync(destDir)) {
    console.log('📁 Creating destination directory:', destDir);
    fs.mkdirSync(destDir, { recursive: true });
  }

  const files = fs.readdirSync(sourceDir);
  let copiedCount = 0;
  let skippedCount = 0;

  files.forEach(file => {
    if (file.toLowerCase().endsWith('.jpg') || file.toLowerCase().endsWith('.png')) {
      const sourcePath = path.join(sourceDir, file);
      const destPath = path.join(destDir, file);
      
      try {
        // Check if file already exists
        if (fs.existsSync(destPath)) {
          console.log(`⏭️  Skipped ${file} (already exists)`);
          skippedCount++;
        } else {
          fs.copyFileSync(sourcePath, destPath);
          console.log(`✅ Copied ${file}`);
          copiedCount++;
        }
      } catch (error) {
        console.error(`❌ Error copying ${file}:`, error.message);
      }
    }
  });

  console.log(`📊 Image copy summary: ${copiedCount} copied, ${skippedCount} skipped`);
  return true;
};

// Function to update image paths in database
const updateImagePaths = async () => {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Get all meals
    const meals = await Meal.find();
    console.log(`📊 Found ${meals.length} meals to update`);

    let updatedCount = 0;
    let skippedCount = 0;

    for (const meal of meals) {
      const currentImage = meal.image;
      
      if (currentImage && currentImage.startsWith('/Food Images/')) {
        // Extract filename from current path
        const filename = currentImage.replace('/Food Images/', '');
        const newImagePath = `/imagesfood/${filename}`;
        
        // Check if the image file exists in the destination directory
        const imagePath = path.join(destDir, filename);
        if (fs.existsSync(imagePath)) {
          // Update the meal's image path
          await Meal.findByIdAndUpdate(meal._id, {
            image: newImagePath
          });
          
          console.log(`✅ Updated "${meal.name}": ${currentImage} → ${newImagePath}`);
          updatedCount++;
        } else {
          console.log(`⚠️  Image not found for "${meal.name}": ${filename}`);
          // Set to a default image or keep original
          await Meal.findByIdAndUpdate(meal._id, {
            image: '/imagesfood/default_filipino.jpg'
          });
          updatedCount++;
        }
      } else if (!currentImage || currentImage === '') {
        // Set default image for meals without images
        await Meal.findByIdAndUpdate(meal._id, {
          image: '/imagesfood/default_filipino.jpg'
        });
        console.log(`✅ Set default image for "${meal.name}"`);
        updatedCount++;
      } else {
        console.log(`⏭️  Skipped "${meal.name}": Already has correct path (${currentImage})`);
        skippedCount++;
      }
    }

    console.log(`📊 Database update summary: ${updatedCount} updated, ${skippedCount} skipped`);

    // Verify the updates
    console.log('\n🔍 Verification - Sample updated meals:');
    const sampleMeals = await Meal.find().limit(5).select('name image');
    sampleMeals.forEach(meal => {
      console.log(`- ${meal.name}: ${meal.image}`);
    });

  } catch (error) {
    console.error('❌ Error updating image paths:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
};

// Main function
const fixImagePaths = async () => {
  console.log('🚀 Starting image path fix process...\n');
  
  // Step 1: Copy images
  const copySuccess = copyImages();
  
  if (!copySuccess) {
    console.error('❌ Image copying failed. Aborting database update.');
    return;
  }

  console.log('\n');
  
  // Step 2: Update database paths
  await updateImagePaths();
  
  console.log('\n✅ Image path fix process completed!');
  console.log('📝 Next steps:');
  console.log('1. Restart your backend server');
  console.log('2. Test image loading in the mobile app');
  console.log('3. Images should now be accessible at: http://your-ip:5000/imagesfood/filename.jpg');
};

// Run the script
if (require.main === module) {
  fixImagePaths();
}

module.exports = { fixImagePaths };
