const mongoose = require('mongoose');
const MealPlan = require('../models/MealPlan');
require('dotenv').config();

async function clearMealPlans() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    console.log('🔍 Checking meal plans in database...');
    
    // Count all meal plans
    const totalCount = await MealPlan.countDocuments();
    console.log(`Found ${totalCount} meal plans in the database`);

    if (totalCount === 0) {
      console.log('✅ No meal plans found to delete');
      return;
    }

    // Get some sample meal plans to show what will be deleted
    const samplePlans = await MealPlan.find().limit(5).select('date templateName user');
    console.log('Sample meal plans to be deleted:');
    samplePlans.forEach(plan => {
      console.log(`- Date: ${plan.date}, Template: ${plan.templateName || 'N/A'}, User: ${plan.user || 'N/A'}`);
    });

    console.log('🗑️  Deleting all meal plans...');
    
    // Delete all meal plans
    const deleteResult = await MealPlan.deleteMany({});
    console.log(`✅ Successfully deleted ${deleteResult.deletedCount} meal plans`);

    // Verify deletion
    const remainingCount = await MealPlan.countDocuments();
    if (remainingCount === 0) {
      console.log('✅ Verification: All meal plans have been successfully removed');
    } else {
      console.log(`⚠️  Warning: ${remainingCount} meal plans still remain`);
    }

  } catch (error) {
    console.error('❌ Error clearing meal plans:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  clearMealPlans();
}

module.exports = { clearMealPlans };
