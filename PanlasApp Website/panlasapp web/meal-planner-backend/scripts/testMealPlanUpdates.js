const mongoose = require('mongoose');
const User = require('../models/User');
const MealPlan = require('../models/MealPlan');
const Meal = require('../models/Meal');
const { updateFutureMealPlansForDietaryChanges } = require('../services/mealPlanUpdateService');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/panlas-app', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function createTestData() {
  console.log('🔧 Creating test data...');
  
  // Create a test user
  const testUser = new User({
    email: '<EMAIL>',
    password: 'testpassword123',
    firstName: 'Test',
    lastName: 'User',
    dietaryPreferences: {
      restrictions: ['Vegetarian'],
      allergies: [],
      dislikedIngredients: [],
      calorieTarget: 2000,
      mealFrequency: 3
    }
  });
  
  await testUser.save();
  console.log(`✅ Created test user: ${testUser._id}`);
  
  // Get some meals from the database
  const meals = await Meal.find().limit(20);
  if (meals.length === 0) {
    console.log('❌ No meals found in database. Please run meal import first.');
    return null;
  }
  
  // Create test meal plans for the next 7 days
  const today = new Date();
  const mealPlans = [];
  
  for (let i = 1; i <= 7; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    const dateStr = date.toISOString().split('T')[0];
    
    // Create meal plan with some meals that might conflict with new dietary preferences
    const mealPlan = new MealPlan({
      user: testUser._id,
      date: dateStr,
      isTemplate: false,
      breakfast: [
        {
          name: meals[0].name,
          mealType: meals[0].mealType,
          category: meals[0].category,
          dietaryTags: meals[0].dietaryTags,
          calories: meals[0].calories,
          protein: meals[0].protein,
          carbs: meals[0].carbs,
          fat: meals[0].fat,
          image: meals[0].image,
          description: meals[0].description,
          ingredients: meals[0].ingredients,
          instructions: meals[0].instructions,
          instanceId: `test_breakfast_${i}`
        }
      ],
      lunch: [
        {
          name: meals[1].name,
          mealType: meals[1].mealType,
          category: meals[1].category,
          dietaryTags: meals[1].dietaryTags,
          calories: meals[1].calories,
          protein: meals[1].protein,
          carbs: meals[1].carbs,
          fat: meals[1].fat,
          image: meals[1].image,
          description: meals[1].description,
          ingredients: meals[1].ingredients,
          instructions: meals[1].instructions,
          instanceId: `test_lunch_${i}`
        }
      ],
      dinner: [
        {
          name: meals[2].name,
          mealType: meals[2].mealType,
          category: meals[2].category,
          dietaryTags: meals[2].dietaryTags,
          calories: meals[2].calories,
          protein: meals[2].protein,
          carbs: meals[2].carbs,
          fat: meals[2].fat,
          image: meals[2].image,
          description: meals[2].description,
          ingredients: meals[2].ingredients,
          instructions: meals[2].instructions,
          instanceId: `test_dinner_${i}`
        }
      ],
      snack: [],
      mealTimes: {
        breakfast: '08:00',
        lunch: '12:00',
        dinner: '18:00'
      },
      isLocked: false,
      completedMeals: { breakfast: [], lunch: [], dinner: [], snack: [] }
    });
    
    await mealPlan.save();
    mealPlans.push(mealPlan);
  }
  
  console.log(`✅ Created ${mealPlans.length} test meal plans`);
  return { user: testUser, mealPlans };
}

async function testDietaryPreferenceUpdate(userId) {
  console.log('\n🧪 Testing dietary preference update...');
  
  // Simulate changing dietary preferences to add allergies and restrictions
  const newDietaryPreferences = {
    restrictions: ['Vegetarian', 'Gluten-Free'], // Added Gluten-Free
    allergies: ['Nuts', 'Dairy'], // Added allergies
    dislikedIngredients: ['Mushrooms'],
    calorieTarget: 1800, // Changed calorie target
    mealFrequency: 3
  };
  
  console.log('📝 New dietary preferences:', newDietaryPreferences);
  
  // Test the meal plan update service
  const updateResult = await updateFutureMealPlansForDietaryChanges(userId, newDietaryPreferences);
  
  console.log('\n📊 Update Results:');
  console.log(`✅ Success: ${updateResult.success}`);
  console.log(`📋 Plans checked: ${updateResult.plansChecked}`);
  console.log(`🔄 Plans updated: ${updateResult.plansUpdated}`);
  console.log(`🍽️ Meals replaced: ${updateResult.mealsReplaced}`);
  
  if (updateResult.updateDetails && updateResult.updateDetails.length > 0) {
    console.log('\n📝 Update Details:');
    updateResult.updateDetails.forEach((detail, index) => {
      console.log(`\n${index + 1}. Date: ${detail.date}`);
      console.log(`   Meals replaced: ${detail.mealsReplaced}`);
      if (detail.replacements && detail.replacements.length > 0) {
        detail.replacements.forEach(replacement => {
          console.log(`   • ${replacement.mealType}: "${replacement.oldMeal}" → "${replacement.newMeal}"`);
          console.log(`     Reason: ${replacement.reason}`);
        });
      }
    });
  }
  
  return updateResult;
}

async function verifyUpdates(userId) {
  console.log('\n🔍 Verifying meal plan updates...');
  
  const today = new Date().toISOString().split('T')[0];
  const updatedPlans = await MealPlan.find({
    user: userId,
    date: { $gte: today },
    isTemplate: false
  }).sort({ date: 1 });
  
  console.log(`\n📋 Found ${updatedPlans.length} future meal plans:`);
  
  updatedPlans.forEach((plan, index) => {
    console.log(`\n${index + 1}. Date: ${plan.date}`);
    console.log(`   Last updated: ${plan.updatedAt}`);
    
    ['breakfast', 'lunch', 'dinner'].forEach(mealType => {
      if (plan[mealType] && plan[mealType].length > 0) {
        plan[mealType].forEach(meal => {
          console.log(`   ${mealType}: "${meal.name}"`);
          if (meal.dietaryTags && meal.dietaryTags.length > 0) {
            console.log(`     Tags: ${meal.dietaryTags.join(', ')}`);
          }
          if (meal.aiReason) {
            console.log(`     AI Reason: ${meal.aiReason}`);
          }
        });
      }
    });
  });
}

async function cleanup(userId) {
  console.log('\n🧹 Cleaning up test data...');
  
  // Delete test meal plans
  await MealPlan.deleteMany({ user: userId });
  console.log('✅ Deleted test meal plans');
  
  // Delete test user
  await User.findByIdAndDelete(userId);
  console.log('✅ Deleted test user');
}

async function runTest() {
  try {
    console.log('🚀 Starting meal plan update test...\n');
    
    // Create test data
    const testData = await createTestData();
    if (!testData) {
      console.log('❌ Failed to create test data');
      return;
    }
    
    const { user } = testData;
    
    // Test dietary preference update
    const updateResult = await testDietaryPreferenceUpdate(user._id);
    
    // Verify the updates
    await verifyUpdates(user._id);
    
    // Show summary
    console.log('\n📊 Test Summary:');
    console.log(`✅ Test completed successfully`);
    console.log(`📋 Plans checked: ${updateResult.plansChecked}`);
    console.log(`🔄 Plans updated: ${updateResult.plansUpdated}`);
    console.log(`🍽️ Meals replaced: ${updateResult.mealsReplaced}`);
    
    // Cleanup
    await cleanup(user._id);
    
    console.log('\n🎉 All tests passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the test
runTest();
