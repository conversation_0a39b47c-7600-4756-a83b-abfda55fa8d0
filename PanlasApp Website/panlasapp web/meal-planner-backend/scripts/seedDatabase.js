const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Meal = require('../models/Meal');
const fs = require('fs');
const path = require('path');

// Load environment variables
dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/meal_planner';

// Function to read the Filipino meal data
const readFilipinioMealData = () => {
  try {
    const filePath = path.join(__dirname, 'filipinoMealData.json');
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading Filipino meal data:', error);
    return [];
  }
};

// Connect to MongoDB and seed data
const seedDatabase = async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB for seeding');

    // Check if meals already exist
    const mealCount = await Meal.countDocuments();
    // if (mealCount > 0) {
    //   console.log(`Database already has ${mealCount} meals. Skipping seed.`);
    //   console.log('If you want to reseed, please drop the collection first.');
    //   return;
    // }

    // Read the Filipino meal data
    const filipinoMeals = readFilipinioMealData();
    
    if (filipinoMeals.length === 0) {
      console.log('No meal data found to seed.');
      return;
    }

    // Prepare meals for insertion
// Inside the seedDatabase.js file, update the mealsToInsert mapping:

const mealsToInsert = filipinoMeals.map(meal => ({
  name: meal.name,
  category: meal.category,
  mealType: meal.mealType || [],
  dietaryTags: meal.dietaryTags || [],
  calories: meal.calories || 0,
  protein: meal.protein || 0,
  carbs: meal.carbs || 0,
  fat: meal.fat || 0,
  image: meal.image || '',
  description: meal.description || '',
  ingredients: meal.ingredients || [],
  instructions: meal.instructions || []
}));


    // Insert meals into the database
    const result = await Meal.insertMany(mealsToInsert);
    console.log(`Successfully seeded ${result.length} Filipino meals to the database.`);
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    mongoose.disconnect();
    console.log('Disconnected from MongoDB after seeding');
  }
};

// Run the seed function
seedDatabase();
