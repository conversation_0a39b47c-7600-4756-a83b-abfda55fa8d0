const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

// Test family member routes
async function testFamilyMemberRoutes() {
  console.log('🧪 Testing Family Member Routes...\n');
  
  // You need to get a real auth token from your browser/app
  const token = process.argv[2];
  
  if (!token) {
    console.log('❌ Please provide an auth token as an argument:');
    console.log('   node scripts/testFamilyMemberRoutes.js YOUR_AUTH_TOKEN');
    console.log('\n🔑 Get your token from browser console:');
    console.log('   localStorage.getItem(\'token\')');
    return;
  }
  
  const headers = {
    'x-auth-token': token,
    'Content-Type': 'application/json'
  };
  
  try {
    // Test 1: Get family members
    console.log('1️⃣ Testing GET /api/users/family-members');
    const getResponse = await axios.get(`${BASE_URL}/api/users/family-members`, { headers });
    console.log('✅ GET Success:', getResponse.status);
    console.log('📊 Family members count:', getResponse.data.familyMembers?.length || 0);
    
    const familyMembers = getResponse.data.familyMembers || [];
    
    if (familyMembers.length === 0) {
      console.log('\n⚠️ No family members found. Adding a test member first...');
      
      // Add a test member
      const testMember = {
        name: 'Test Member',
        dateOfBirth: '1990-01-01',
        dietaryPreferences: {
          restrictions: ['Vegetarian'],
          allergies: ['Peanuts'],
          dislikedIngredients: ['Mushrooms']
        }
      };
      
      const addResponse = await axios.post(`${BASE_URL}/api/users/family-members`, testMember, { headers });
      console.log('✅ Test member added');
      
      // Get updated list
      const updatedResponse = await axios.get(`${BASE_URL}/api/users/family-members`, { headers });
      familyMembers.push(...(updatedResponse.data.familyMembers || []));
    }
    
    if (familyMembers.length > 0) {
      const testMember = familyMembers[0];
      console.log(`\n2️⃣ Testing PUT /api/users/family-members/${testMember._id}`);
      
      // Test 2: Update family member
      const updateData = {
        name: testMember.name + ' (Updated)',
        dateOfBirth: testMember.dateOfBirth,
        dietaryPreferences: {
          restrictions: ['Vegetarian', 'Gluten-Free'],
          allergies: ['Peanuts', 'Tree Nuts'],
          dislikedIngredients: ['Mushrooms', 'Onions']
        }
      };
      
      console.log('📤 Sending update request...');
      console.log('🔗 URL:', `${BASE_URL}/api/users/family-members/${testMember._id}`);
      console.log('📦 Data:', JSON.stringify(updateData, null, 2));
      
      const updateResponse = await axios.put(
        `${BASE_URL}/api/users/family-members/${testMember._id}`,
        updateData,
        { headers }
      );
      
      console.log('✅ PUT Success:', updateResponse.status);
      console.log('📊 Response:', JSON.stringify(updateResponse.data, null, 2));
      
      // Test 3: Verify the update
      console.log('\n3️⃣ Verifying update...');
      const verifyResponse = await axios.get(`${BASE_URL}/api/users/family-members`, { headers });
      const updatedMember = verifyResponse.data.familyMembers?.find(m => m._id === testMember._id);
      
      if (updatedMember) {
        console.log('✅ Update verified successfully');
        console.log('📝 Updated name:', updatedMember.name);
        console.log('🥗 Updated restrictions:', updatedMember.dietaryPreferences?.restrictions);
      } else {
        console.log('❌ Could not verify update');
      }
      
    } else {
      console.log('❌ No family members available for testing');
    }
    
    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.log('\n❌ Test failed:');
    console.log('🔗 URL:', error.config?.url);
    console.log('📤 Method:', error.config?.method?.toUpperCase());
    console.log('📊 Status:', error.response?.status);
    console.log('📄 Response:', JSON.stringify(error.response?.data, null, 2));
    console.log('🔍 Error:', error.message);
    
    if (error.response?.status === 404) {
      console.log('\n🔧 Debugging 404 Error:');
      console.log('1. Check if the backend server is running on port 5000');
      console.log('2. Verify the route is properly loaded in userRoutes.js');
      console.log('3. Check if the updateFamilyMember function exists in userController.js');
      console.log('4. Restart the backend server to reload routes');
    }
    
    if (error.response?.status === 401) {
      console.log('\n🔧 Debugging 401 Error:');
      console.log('1. Check if the auth token is valid');
      console.log('2. Make sure you\'re logged in');
      console.log('3. Get a fresh token from localStorage.getItem(\'token\')');
    }
  }
}

// Run the test
if (require.main === module) {
  testFamilyMemberRoutes().catch(error => {
    console.error('❌ Test runner failed:', error.message);
    process.exit(1);
  });
}

module.exports = { testFamilyMemberRoutes };
