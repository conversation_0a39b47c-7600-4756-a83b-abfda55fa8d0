const mongoose = require('mongoose');
require('dotenv').config();

// Get MongoDB URI from environment variable
const MONGODB_URI = process.env.MONGODB_URI;

console.log('Connecting to MongoDB...');

// Connect to MongoDB
mongoose.connect(MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
}).then(async () => {
  console.log('MongoDB connected successfully');
  
  try {
    // Get the MealPlan collection
    const mealPlanCollection = mongoose.connection.db.collection('mealplans');
    console.log('Found MealPlan collection');
    
    // Count all meal plans
    const count = await mealPlanCollection.countDocuments();
    console.log(`Total meal plans in the database: ${count}`);
    
    // Get all meal plans
    const mealPlans = await mealPlanCollection.find().toArray();
    console.log(`Retrieved ${mealPlans.length} meal plans`);
    
    // Check if meal plans have user field
    const mealPlansWithoutUser = mealPlans.filter(plan => !plan.user);
    console.log(`Meal plans without user field: ${mealPlansWithoutUser.length} out of ${mealPlans.length}`);
    
    // Get the User collection
    const userCollection = mongoose.connection.db.collection('users');
    console.log('Found User collection');
    
    // Get all users
    const users = await userCollection.find().toArray();
    console.log(`Retrieved ${users.length} users`);
    
    if (users.length === 0) {
      console.log('No users found in the database!');
      process.exit(1);
    }
    
    // Use the first user as default
    const defaultUser = users[0];
    console.log(`Using default user: ${defaultUser._id} (${defaultUser.username || defaultUser.email})`);
    
    // Fix meal plans without user
    if (mealPlansWithoutUser.length > 0) {
      console.log(`Fixing ${mealPlansWithoutUser.length} meal plans without user...`);
      
      for (const plan of mealPlansWithoutUser) {
        console.log(`Updating meal plan ${plan._id} for date ${plan.date}...`);
        
        const result = await mealPlanCollection.updateOne(
          { _id: plan._id },
          { $set: { user: defaultUser._id } }
        );
        
        console.log(`Updated meal plan ${plan._id}: ${result.modifiedCount} document modified`);
      }
      
      console.log('All meal plans without user have been fixed!');
    }
    
    // Check for duplicate meal plans (same user and date)
    console.log('Checking for duplicate meal plans...');
    
    const userDateCounts = {};
    mealPlans.forEach(plan => {
      if (plan.user) {
        const key = `${plan.user.toString()}-${plan.date}`;
        userDateCounts[key] = (userDateCounts[key] || 0) + 1;
      }
    });
    
    const duplicates = Object.entries(userDateCounts)
      .filter(([key, count]) => count > 1)
      .map(([key]) => key);
    
    console.log(`Found ${duplicates.length} duplicate user-date combinations`);
    
    if (duplicates.length > 0) {
      console.log('Duplicate user-date combinations:');
      for (const key of duplicates) {
        const [userId, date] = key.split('-');
        console.log(`- User ${userId}, Date: ${date}`);
        
        // Find all meal plans for this user and date
        const userDatePlans = mealPlans.filter(
          plan => plan.user && plan.user.toString() === userId && plan.date === date
        );
        
        console.log(`  Found ${userDatePlans.length} meal plans for this combination`);
        
        // Keep the most recent one and delete the others
        userDatePlans.sort((a, b) => {
          return new Date(b.updatedAt || 0) - new Date(a.updatedAt || 0);
        });
        
        const planToKeep = userDatePlans[0];
        console.log(`  Keeping meal plan ${planToKeep._id} (most recent)`);
        
        for (let i = 1; i < userDatePlans.length; i++) {
          const planToDelete = userDatePlans[i];
          console.log(`  Deleting duplicate meal plan ${planToDelete._id}`);
          
          await mealPlanCollection.deleteOne({ _id: planToDelete._id });
        }
      }
      
      console.log('All duplicate meal plans have been fixed!');
    }
    
    console.log('Meal plan check and fix completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error fixing meal plans:', error);
    process.exit(1);
  }
});
