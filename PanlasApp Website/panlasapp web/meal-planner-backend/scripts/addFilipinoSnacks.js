const mongoose = require('mongoose');
const Meal = require('../models/Meal');
require('dotenv').config();

// Connect to MongoDB using the same connection as the main app
mongoose.connect(process.env.MONGODB_URI);

const filipinoSnacks = [
  {
    name: "<PERSON>ron",
    mealType: ["snack"],
    category: ["Dessert"],
    dietaryTags: ["sweet", "fried"],
    calories: 180,
    protein: 3,
    carbs: 28,
    fat: 7,
    image: "/imagesfood/turon.jpg",
    description: "Deep-fried spring roll filled with sliced banana and jackfruit strips, rolled in brown sugar.",
    ingredients: ["banana", "jackfruit", "brown sugar", "spring roll wrapper", "cooking oil"],
    instructions: ["Slice banana and jackfruit", "Roll in brown sugar", "Wrap in spring roll wrapper", "Deep fry until golden"],
    allergens: ["Gluten"],
    rating: 4.7,
    prepTime: 20,
    price: 25,
    region: "Philippines",
    servingSize: 2,
    dietType: {
      isVegetarian: true,
      isVegan: false,
      isGlutenFree: false,
      isDairyFree: true,
      isNutFree: true,
      isLowCarb: false,
      isKeto: false,
      isPescatarian: true,
      isHalal: true
    }
  },
  {
    name: "Banana Cue",
    mealType: ["snack"],
    category: ["Dessert"],
    dietaryTags: ["sweet", "fried", "street food"],
    calories: 150,
    protein: 2,
    carbs: 35,
    fat: 3,
    image: "/imagesfood/banana_cue.jpg",
    description: "Deep-fried banana coated with brown sugar, served on a stick.",
    ingredients: ["saba banana", "brown sugar", "cooking oil", "wooden stick"],
    instructions: ["Peel bananas", "Roll in brown sugar", "Deep fry until caramelized", "Serve on stick"],
    allergens: [],
    rating: 4.5,
    prepTime: 15,
    price: 15,
    region: "Philippines",
    servingSize: 1,
    dietType: {
      isVegetarian: true,
      isVegan: true,
      isGlutenFree: true,
      isDairyFree: true,
      isNutFree: true,
      isLowCarb: false,
      isKeto: false,
      isPescatarian: true,
      isHalal: true
    }
  },
  {
    name: "Kamote Cue",
    mealType: ["snack"],
    category: ["Dessert"],
    dietaryTags: ["sweet", "fried", "street food"],
    calories: 140,
    protein: 2,
    carbs: 32,
    fat: 2,
    image: "/imagesfood/kamote_cue.jpg",
    description: "Deep-fried sweet potato coated with brown sugar, served on a stick.",
    ingredients: ["sweet potato", "brown sugar", "cooking oil", "wooden stick"],
    instructions: ["Peel and slice sweet potato", "Roll in brown sugar", "Deep fry until golden", "Serve on stick"],
    allergens: [],
    rating: 4.4,
    prepTime: 20,
    price: 12,
    region: "Philippines",
    servingSize: 1,
    dietType: {
      isVegetarian: true,
      isVegan: true,
      isGlutenFree: true,
      isDairyFree: true,
      isNutFree: true,
      isLowCarb: false,
      isKeto: false,
      isPescatarian: true,
      isHalal: true
    }
  },
  {
    name: "Fishball",
    mealType: ["snack"],
    category: ["Seafood"],
    dietaryTags: ["street food", "fried"],
    calories: 120,
    protein: 8,
    carbs: 12,
    fat: 5,
    image: "/imagesfood/fishball.jpg",
    description: "Deep-fried fish balls served with sweet and sour sauce or spicy vinegar.",
    ingredients: ["fish paste", "flour", "cornstarch", "seasonings", "cooking oil"],
    instructions: ["Mix fish paste with flour", "Form into balls", "Deep fry until golden", "Serve with sauce"],
    allergens: ["Fish"],
    rating: 4.3,
    prepTime: 25,
    price: 20,
    region: "Philippines",
    servingSize: 10,
    dietType: {
      isVegetarian: false,
      isVegan: false,
      isGlutenFree: false,
      isDairyFree: true,
      isNutFree: true,
      isLowCarb: false,
      isKeto: false,
      isPescatarian: true,
      isHalal: false
    }
  },
  {
    name: "Kwek-kwek",
    mealType: ["snack"],
    category: ["Meat"],
    dietaryTags: ["street food", "fried"],
    calories: 160,
    protein: 12,
    carbs: 15,
    fat: 8,
    image: "/imagesfood/kwek_kwek.jpg",
    description: "Deep-fried quail eggs coated in orange batter, served with spicy vinegar.",
    ingredients: ["quail eggs", "flour", "cornstarch", "food coloring", "seasonings"],
    instructions: ["Hard boil quail eggs", "Coat in orange batter", "Deep fry until crispy", "Serve with vinegar"],
    allergens: ["Eggs", "Gluten"],
    rating: 4.2,
    prepTime: 30,
    price: 25,
    region: "Philippines",
    servingSize: 8,
    dietType: {
      isVegetarian: true,
      isVegan: false,
      isGlutenFree: false,
      isDairyFree: true,
      isNutFree: true,
      isLowCarb: false,
      isKeto: false,
      isPescatarian: true,
      isHalal: true
    }
  },
  {
    name: "Taho",
    mealType: ["snack"],
    category: ["Dessert"],
    dietaryTags: ["sweet", "soft tofu", "traditional"],
    calories: 100,
    protein: 6,
    carbs: 18,
    fat: 2,
    image: "/imagesfood/taho.jpg",
    description: "Soft tofu with arnibal (brown sugar syrup) and sago pearls, served warm.",
    ingredients: ["soft tofu", "brown sugar", "sago pearls", "water"],
    instructions: ["Prepare soft tofu", "Make arnibal syrup", "Cook sago pearls", "Serve warm in cup"],
    allergens: ["Soy"],
    rating: 4.6,
    prepTime: 45,
    price: 10,
    region: "Philippines",
    servingSize: 1,
    dietType: {
      isVegetarian: true,
      isVegan: true,
      isGlutenFree: true,
      isDairyFree: true,
      isNutFree: true,
      isLowCarb: false,
      isKeto: false,
      isPescatarian: true,
      isHalal: true
    }
  },
  {
    name: "Halo-halo",
    mealType: ["snack"],
    category: ["Dessert"],
    dietaryTags: ["sweet", "cold", "mixed dessert"],
    calories: 250,
    protein: 5,
    carbs: 45,
    fat: 8,
    image: "/imagesfood/halo_halo.jpg",
    description: "Mixed dessert with shaved ice, evaporated milk, and various sweet ingredients.",
    ingredients: ["shaved ice", "evaporated milk", "ube", "leche flan", "sweet beans", "jackfruit", "ice cream"],
    instructions: ["Layer ingredients in tall glass", "Add shaved ice", "Top with milk and ice cream", "Mix before eating"],
    allergens: ["Dairy", "Eggs"],
    rating: 4.8,
    prepTime: 15,
    price: 80,
    region: "Philippines",
    servingSize: 1,
    dietType: {
      isVegetarian: true,
      isVegan: false,
      isGlutenFree: true,
      isDairyFree: false,
      isNutFree: true,
      isLowCarb: false,
      isKeto: false,
      isPescatarian: true,
      isHalal: true
    }
  },
  {
    name: "Mais con Yelo",
    mealType: ["snack"],
    category: ["Dessert"],
    dietaryTags: ["sweet", "cold", "corn"],
    calories: 180,
    protein: 4,
    carbs: 38,
    fat: 3,
    image: "/imagesfood/mais_con_yelo.jpg",
    description: "Sweet corn kernels with shaved ice, milk, and sugar.",
    ingredients: ["sweet corn kernels", "shaved ice", "evaporated milk", "sugar"],
    instructions: ["Cook corn kernels", "Add to glass with ice", "Pour milk and sugar", "Mix well"],
    allergens: ["Dairy"],
    rating: 4.3,
    prepTime: 10,
    price: 35,
    region: "Philippines",
    servingSize: 1,
    dietType: {
      isVegetarian: true,
      isVegan: false,
      isGlutenFree: true,
      isDairyFree: false,
      isNutFree: true,
      isLowCarb: false,
      isKeto: false,
      isPescatarian: true,
      isHalal: true
    }
  },
  {
    name: "Biko",
    mealType: ["snack"],
    category: ["Rice"],
    dietaryTags: ["sweet", "sticky rice", "traditional"],
    calories: 200,
    protein: 3,
    carbs: 42,
    fat: 4,
    image: "/imagesfood/biko.jpg",
    description: "Sweet sticky rice cake topped with latik (coconut curd).",
    ingredients: ["glutinous rice", "coconut milk", "brown sugar", "salt"],
    instructions: ["Cook glutinous rice", "Mix with coconut milk and sugar", "Simmer until thick", "Top with latik"],
    allergens: [],
    rating: 4.5,
    prepTime: 60,
    price: 40,
    region: "Philippines",
    servingSize: 4,
    dietType: {
      isVegetarian: true,
      isVegan: true,
      isGlutenFree: true,
      isDairyFree: true,
      isNutFree: false,
      isLowCarb: false,
      isKeto: false,
      isPescatarian: true,
      isHalal: true
    }
  },
  {
    name: "Puto",
    mealType: ["snack"],
    category: ["Rice"],
    dietaryTags: ["sweet", "steamed", "rice cake"],
    calories: 120,
    protein: 3,
    carbs: 25,
    fat: 2,
    image: "/imagesfood/puto.jpg",
    description: "Steamed rice cake, often topped with cheese or salted egg.",
    ingredients: ["rice flour", "sugar", "baking powder", "coconut milk", "cheese"],
    instructions: ["Mix rice flour with ingredients", "Steam in molds", "Top with cheese", "Serve warm"],
    allergens: ["Dairy"],
    rating: 4.4,
    prepTime: 30,
    price: 30,
    region: "Philippines",
    servingSize: 6,
    dietType: {
      isVegetarian: true,
      isVegan: false,
      isGlutenFree: true,
      isDairyFree: false,
      isNutFree: true,
      isLowCarb: false,
      isKeto: false,
      isPescatarian: true,
      isHalal: true
    }
  }
];

async function addFilipinoSnacks() {
  try {
    console.log('Adding Filipino snacks to the database...');
    
    // Check if snacks already exist to avoid duplicates
    for (const snack of filipinoSnacks) {
      const existingSnack = await Meal.findOne({ name: snack.name });
      if (!existingSnack) {
        await Meal.create(snack);
        console.log(`✅ Added: ${snack.name}`);
      } else {
        console.log(`⚠️  Already exists: ${snack.name}`);
      }
    }
    
    console.log('✅ Filipino snacks added successfully!');
  } catch (error) {
    console.error('❌ Error adding Filipino snacks:', error);
  } finally {
    mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
addFilipinoSnacks();
