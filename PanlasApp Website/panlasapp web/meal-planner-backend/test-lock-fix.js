require('dotenv').config();
const mongoose = require('mongoose');
const MealPlan = require('./models/MealPlan');

async function testLockFix() {
  try {
    console.log('🔍 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    const testDate = '2025-08-14';
    const userId = '685883ea3cc2df1d683b8714'; // User ID from the debug output

    console.log(`\n🔒 Testing lock functionality for date: ${testDate}`);
    console.log(`User ID: ${userId}`);

    // Test the OLD way (using Date object) - this should fail
    console.log('\n❌ Testing OLD method (using Date object):');
    try {
      const mealPlanOld = await MealPlan.findOne({
        user: userId,
        date: new Date(testDate)
      });
      console.log(`Result: ${mealPlanOld ? '✅ Found' : '❌ Not found'}`);
    } catch (error) {
      console.log(`Error: ${error.message}`);
    }

    // Test the NEW way (using string) - this should work
    console.log('\n✅ Testing NEW method (using string):');
    try {
      const dateStr = typeof testDate === 'string' ? testDate : new Date(testDate).toISOString().split('T')[0];
      console.log(`Converted date: "${dateStr}"`);
      
      const mealPlanNew = await MealPlan.findOne({
        user: userId,
        date: dateStr
      });
      
      if (mealPlanNew) {
        console.log('✅ Found meal plan!');
        console.log(`- Current lock status: ${mealPlanNew.isLocked}`);
        
        // Test toggling the lock
        console.log('\n🔄 Testing lock toggle...');
        const newLockStatus = !mealPlanNew.isLocked;
        mealPlanNew.isLocked = newLockStatus;
        await mealPlanNew.save();
        
        console.log(`✅ Lock status updated to: ${newLockStatus}`);
        
        // Verify the change
        const verifyPlan = await MealPlan.findOne({
          user: userId,
          date: dateStr
        });
        console.log(`✅ Verified lock status: ${verifyPlan.isLocked}`);
        
        // Toggle back to original state
        verifyPlan.isLocked = !newLockStatus;
        await verifyPlan.save();
        console.log(`✅ Restored original lock status: ${verifyPlan.isLocked}`);
        
      } else {
        console.log('❌ Not found');
      }
    } catch (error) {
      console.log(`Error: ${error.message}`);
    }

    console.log('\n🎉 Test completed successfully!');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

testLockFix();
