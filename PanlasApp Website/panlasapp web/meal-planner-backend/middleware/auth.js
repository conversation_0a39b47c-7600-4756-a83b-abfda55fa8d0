// middleware/auth.js
const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Required authentication middleware
const auth = (req, res, next) => {
  try {
    // Get token from header - support both formats
    let token = null;

    // Check for Authorization: Bearer <token>
    const authHeader = req.header('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.replace('Bearer ', '');
    }

    // Check for x-auth-token: <token> (legacy format)
    if (!token) {
      token = req.header('x-auth-token');
    }

    if (!token) {
      return res.status(401).json({ message: 'Authentication failed: No token provided' });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Add user from payload
    req.user = decoded;
    next();
  } catch (error) {
    res.status(401).json({ message: 'Authentication failed' });
  }
};

// Optional authentication middleware - doesn't fail if no token
const optionalAuth = (req, res, next) => {
  try {
    // Get token from header - support both formats
    let token = null;

    // Check for Authorization: Bearer <token>
    const authHeader = req.header('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.replace('Bearer ', '');
    }

    // Check for x-auth-token: <token> (legacy format)
    if (!token) {
      token = req.header('x-auth-token');
    }

    if (token) {
      // Verify token if present
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      req.user = decoded;
    }
    // Continue regardless of whether token is present or valid
    next();
  } catch (error) {
    // Continue without user if token is invalid
    next();
  }
};

// Enhanced authentication middleware that also checks terms acceptance
const authWithTerms = async (req, res, next) => {
  try {
    // First do regular authentication
    let token = null;

    // Check for Authorization: Bearer <token>
    const authHeader = req.header('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.replace('Bearer ', '');
    }

    // Check for x-auth-token: <token> (legacy format)
    if (!token) {
      token = req.header('x-auth-token');
    }

    if (!token) {
      return res.status(401).json({
        message: 'Authentication failed: No token provided',
        code: 'NO_TOKEN'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Get user from database to check terms acceptance and account status
    const user = await User.findById(decoded.id).select('termsAccepted termsAcceptedAt termsVersion isActive isEmailVerified');

    if (!user) {
      return res.status(401).json({
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Check if user account is active
    if (!user.isActive) {
      return res.status(403).json({
        message: 'Account is disabled',
        code: 'ACCOUNT_DISABLED'
      });
    }

    // Check if user has accepted terms and conditions
    if (!user.termsAccepted || !user.termsAcceptedAt) {
      return res.status(403).json({
        message: 'Terms and conditions acceptance required',
        code: 'TERMS_NOT_ACCEPTED',
        requiresTermsAcceptance: true
      });
    }

    // Add user from payload
    req.user = decoded;
    req.userTerms = {
      accepted: user.termsAccepted,
      acceptedAt: user.termsAcceptedAt,
      version: user.termsVersion
    };

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        message: 'Authentication failed: Invalid token',
        code: 'INVALID_TOKEN'
      });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        message: 'Authentication failed: Token expired',
        code: 'TOKEN_EXPIRED'
      });
    }

    console.error('Auth with terms error:', error);
    res.status(500).json({
      message: 'Authentication error',
      code: 'AUTH_ERROR'
    });
  }
};

module.exports = auth;
module.exports.optionalAuth = optionalAuth;
module.exports.authWithTerms = authWithTerms;
