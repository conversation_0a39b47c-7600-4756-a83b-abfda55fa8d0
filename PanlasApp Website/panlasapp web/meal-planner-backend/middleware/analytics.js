const Analytics = require('../models/Analytics');
const User = require('../models/User');
const { v4: uuidv4 } = require('uuid');

// Device detection utility
const detectDevice = (userAgent) => {
  if (!userAgent) return { platform: 'unknown', isMobile: false };
  
  const ua = userAgent.toLowerCase();
  
  // Mobile detection
  const mobileRegex = /mobile|android|iphone|ipad|phone|blackberry|opera mini|iemobile|wpdesktop/i;
  const tabletRegex = /tablet|ipad|playbook|silk/i;
  
  let platform = 'desktop';
  let isMobile = false;
  
  if (tabletRegex.test(ua)) {
    platform = 'tablet';
    isMobile = true;
  } else if (mobileRegex.test(ua)) {
    platform = 'mobile';
    isMobile = true;
  }
  
  // Browser detection
  let browser = 'unknown';
  let browserVersion = '';
  
  if (ua.includes('chrome')) {
    browser = 'Chrome';
    const match = ua.match(/chrome\/(\d+)/);
    browserVersion = match ? match[1] : '';
  } else if (ua.includes('firefox')) {
    browser = 'Firefox';
    const match = ua.match(/firefox\/(\d+)/);
    browserVersion = match ? match[1] : '';
  } else if (ua.includes('safari') && !ua.includes('chrome')) {
    browser = 'Safari';
    const match = ua.match(/version\/(\d+)/);
    browserVersion = match ? match[1] : '';
  } else if (ua.includes('edge')) {
    browser = 'Edge';
    const match = ua.match(/edge\/(\d+)/);
    browserVersion = match ? match[1] : '';
  }
  
  // OS detection
  let os = 'unknown';
  let osVersion = '';
  
  if (ua.includes('windows')) {
    os = 'Windows';
    if (ua.includes('windows nt 10')) osVersion = '10';
    else if (ua.includes('windows nt 6.3')) osVersion = '8.1';
    else if (ua.includes('windows nt 6.2')) osVersion = '8';
    else if (ua.includes('windows nt 6.1')) osVersion = '7';
  } else if (ua.includes('mac os')) {
    os = 'macOS';
    const match = ua.match(/mac os x (\d+_\d+)/);
    osVersion = match ? match[1].replace('_', '.') : '';
  } else if (ua.includes('android')) {
    os = 'Android';
    const match = ua.match(/android (\d+\.?\d*)/);
    osVersion = match ? match[1] : '';
  } else if (ua.includes('iphone') || ua.includes('ipad')) {
    os = 'iOS';
    const match = ua.match(/os (\d+_\d+)/);
    osVersion = match ? match[1].replace('_', '.') : '';
  } else if (ua.includes('linux')) {
    os = 'Linux';
  }
  
  return {
    platform,
    isMobile,
    browser,
    browserVersion,
    os,
    osVersion
  };
};

// Get IP address from request
const getClientIP = (req) => {
  return req.ip || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress || 
         (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
         req.headers['x-forwarded-for']?.split(',')[0] ||
         req.headers['x-real-ip'] ||
         'unknown';
};

// Analytics tracking middleware
const trackAnalytics = (event, options = {}) => {
  return async (req, res, next) => {
    try {
      // Skip if no user (for optional auth routes)
      if (!req.user && !options.allowAnonymous) {
        return next();
      }

      // Generate session ID if not exists
      if (!req.sessionId) {
        req.sessionId = req.headers['x-session-id'] || uuidv4();
      }

      const userAgent = req.headers['user-agent'] || '';
      const deviceInfo = detectDevice(userAgent);
      const ipAddress = getClientIP(req);

      // Get user context if user exists
      let userContext = {
        isAdmin: false,
        isActive: true,
        isVerified: false,
        userType: 'user',
        accountAge: 0,
        lastLoginDays: 0
      };

      if (req.user) {
        try {
          const user = await User.findById(req.user.id);
          if (user) {
            const accountAge = Math.floor((Date.now() - user.createdAt) / (1000 * 60 * 60 * 24));
            const lastLoginDays = user.lastLogin ? 
              Math.floor((Date.now() - user.lastLogin) / (1000 * 60 * 60 * 24)) : 0;

            userContext = {
              isAdmin: user.isAdmin || false,
              isActive: user.isActive || false,
              isVerified: user.isEmailVerified || false,
              userType: user.isAdmin ? 'admin' : 'user',
              accountAge,
              lastLoginDays
            };
          }
        } catch (error) {
          console.error('Error fetching user context for analytics:', error);
        }
      }

      // Prepare analytics data
      const analyticsData = {
        user: req.user ? req.user.id : null,
        sessionId: req.sessionId,
        event: event,
        eventData: options.eventData || {},
        deviceInfo: {
          userAgent,
          ...deviceInfo
        },
        location: {
          ipAddress,
          timezone: req.headers['x-timezone'] || 'unknown'
        },
        userContext,
        sessionInfo: {
          isNewSession: !req.headers['x-session-id'],
          referrer: req.headers.referer || req.headers.referrer || 'direct',
          entryPage: req.originalUrl
        },
        apiInfo: {
          endpoint: req.originalUrl,
          method: req.method
        },
        timestamp: new Date()
      };

      // Add custom event data if provided
      if (options.customData && typeof options.customData === 'function') {
        const customData = options.customData(req, res);
        analyticsData.eventData = { ...analyticsData.eventData, ...customData };
      }

      // Store analytics data (non-blocking)
      setImmediate(async () => {
        try {
          await Analytics.create(analyticsData);
        } catch (error) {
          console.error('Error saving analytics:', error);
        }
      });

      // Add analytics data to request for potential use in controllers
      req.analytics = analyticsData;

      next();
    } catch (error) {
      console.error('Analytics middleware error:', error);
      next(); // Continue even if analytics fails
    }
  };
};

// Track API calls
const trackAPICall = (req, res, next) => {
  const startTime = Date.now();
  
  // Override res.json to capture response
  const originalJson = res.json;
  res.json = function(data) {
    const responseTime = Date.now() - startTime;
    
    // Track API call analytics
    setImmediate(async () => {
      try {
        if (req.user) {
          const user = await User.findById(req.user.id);
          if (user && user.isEmailVerified) { // Only track verified users
            await Analytics.create({
              user: req.user.id,
              sessionId: req.sessionId || uuidv4(),
              event: 'api_call',
              eventData: {
                endpoint: req.originalUrl,
                method: req.method,
                statusCode: res.statusCode,
                responseTime,
                success: res.statusCode < 400
              },
              deviceInfo: detectDevice(req.headers['user-agent']),
              location: {
                ipAddress: getClientIP(req)
              },
              userContext: {
                isAdmin: user.isAdmin || false,
                isActive: user.isActive || false,
                isVerified: user.isEmailVerified || false,
                userType: user.isAdmin ? 'admin' : 'user'
              },
              apiInfo: {
                endpoint: req.originalUrl,
                method: req.method,
                statusCode: res.statusCode,
                responseTime
              },
              performance: {
                responseTime
              }
            });
          }
        }
      } catch (error) {
        console.error('Error tracking API call:', error);
      }
    });
    
    return originalJson.call(this, data);
  };
  
  next();
};

module.exports = {
  trackAnalytics,
  trackAPICall,
  detectDevice,
  getClientIP
};
