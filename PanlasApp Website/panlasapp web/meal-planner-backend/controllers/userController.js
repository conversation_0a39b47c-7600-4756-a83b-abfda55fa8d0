const User = require('../models/User');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');
const emailService = require('../services/emailService');
const Analytics = require('../models/Analytics');
const Activity = require('../models/Activity');
const ActivityService = require('../services/activityService');
const { detectDevice, getClientIP } = require('../middleware/analytics');
const { v4: uuidv4 } = require('uuid');

exports.register = async (req, res) => {
  console.log('Register endpoint hit with data:', req.body);

  try {
    const { username, email, password, firstName, lastName, dateOfBirth, gender, barangay, termsAccepted } = req.body;

    // Validate required fields
    if (!username || !email || !password) {
      console.log('Missing required fields');
      return res.status(400).json({
         message: 'Username, email and password are required'
      });
    }

    // Terms acceptance will be handled during first login, not registration

    console.log('Checking if user already exists');
    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [
        { email: email.toLowerCase() },
        { username: username }
      ]
    });

    if (existingUser) {
      console.log('User already exists:', existingUser.email);

      // Check if it's the same email or username
      const isEmailTaken = existingUser.email === email.toLowerCase();
      const isUsernameTaken = existingUser.username === username;

      let message = 'An account already exists with this ';
      if (isEmailTaken && isUsernameTaken) {
        message += 'email and username.';
      } else if (isEmailTaken) {
        message += 'email address.';
      } else {
        message += 'username.';
      }

      return res.status(409).json({
        message: message + ' Please login instead.',
        userExists: true,
        existingEmail: isEmailTaken ? existingUser.email : null,
        existingUsername: isUsernameTaken ? existingUser.username : null,
        isEmailVerified: existingUser.isEmailVerified,
        redirectToLogin: true
      });
    }

    console.log('Creating new user with data:', {
      username,
      email,
      firstName,
      lastName,
      dateOfBirth,
      gender,
      barangay
    });

    // Generate email verification token and OTP
    const verificationToken = emailService.generateVerificationToken();
    const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
    const emailOTP = emailService.generateOTP();
    const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Create new user
    const user = new User({
      username,
      email,
      password,
      firstName,
      lastName,
      dateOfBirth,
      gender,
      barangay,
      emailVerificationToken: verificationToken,
      emailVerificationExpires: verificationExpires,
      emailOTP: emailOTP,
      emailOTPExpires: otpExpires,
      isEmailVerified: false
      // Terms acceptance will be handled during first login
    });

    console.log('About to save user to database');
    console.log('MongoDB connection state:', mongoose.connection.readyState);
    // 0 = disconnected, 1 = connected, 2 = connecting, 3 = disconnecting

    // Get the current database name
    const dbName = mongoose.connection.name;
    console.log('Current database name:', dbName);

    // List all collections
    try {
      const collections = await mongoose.connection.db.listCollections().toArray();
      console.log('Collections in database:');
      collections.forEach(collection => {
        console.log('- ' + collection.name);
      });
    } catch (err) {
      console.error('Error listing collections:', err);
    }

    // Save user to database with explicit promise handling
    try {
      const savedUser = await user.save();
      console.log('User saved successfully with ID:', savedUser._id);
      console.log('Saved user document:', {
        id: savedUser._id,
        username: savedUser.username,
        email: savedUser.email,
        firstName: savedUser.firstName,
        lastName: savedUser.lastName
      });
    } catch (saveError) {
      console.error('Error saving user:', saveError);
      throw saveError; // Re-throw to be caught by the outer catch block
    }

    // Send verification email with OTP
    const emailSent = await emailService.sendVerificationEmail(
      user.email,
      user.username || user.firstName,
      verificationToken,
      emailOTP
    );

    if (!emailSent) {
      console.warn('Failed to send verification email, but user was created');
    }

    console.log('User registration completed');

    // Return success without token (user needs to verify email first)
    res.status(201).json({
      message: 'Registration successful! Please check your email for a verification code.',
      user: {
        id: user._id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        isEmailVerified: user.isEmailVerified
      },
      emailSent,
      requiresVerification: true
    });
    console.log('Response sent successfully');

    // Double-check that the user was saved by trying to retrieve it
    setTimeout(async () => {
      try {
        const retrievedUser = await User.findById(user._id);
        if (retrievedUser) {
          console.log('Successfully retrieved user from database after save');
        } else {
          console.error('User could not be retrieved from database after save!');
        }
      } catch (err) {
        console.error('Error retrieving user after save:', err);
      }
    }, 1000); // Check after 1 second

  } catch (error) {
    console.error('Registration error details:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({
       message: 'Registration failed. Please try again.',
      error: error.message
    });
  }
};

// Rest of your controller code remains the same
exports.login = async (req, res) => {
  try {
    console.log('🔐 LOGIN ATTEMPT:', { email: req.body.email });
    const { email, password } = req.body;

    // Validate required fields
    if (!email || !password) {
      console.log('❌ Missing email or password');
      return res.status(400).json({ message: 'Email and password are required' });
    }

    console.log('🔍 Finding user by email:', email);
    // Find user by email
    const user = await User.findOne({ email });
    if (!user) {
      console.log('❌ User not found');
      return res.status(401).json({ message: 'Invalid email or password' });
    }

    console.log('✅ User found:', { id: user._id, email: user.email, isActive: user.isActive, isEmailVerified: user.isEmailVerified });

    // Check if user account is active
    if (!user.isActive) {
      console.log('❌ User account is not active');
      return res.status(403).json({
        message: 'Your account has been disabled. Please contact the administrator for assistance.'
      });
    }

    // Check if email is verified
    if (!user.isEmailVerified) {
      console.log('❌ Email not verified');
      return res.status(403).json({
        message: 'Please verify your email address before logging in. Check your email for the verification link.',
        emailVerified: false
      });
    }

    console.log('🔑 Comparing password...');
    // Check password
    const isMatch = await user.comparePassword(password);
    console.log('🔑 Password comparison result:', isMatch);
    if (!isMatch) {
      // Record failed login attempt
      if (req.rateLimiter && req.clientIP) {
        const isBlocked = req.rateLimiter.recordFailedAttempt(req.clientIP);
        if (isBlocked) {
          const timeUntilUnblock = req.rateLimiter.getTimeUntilUnblock(req.clientIP);
          return res.status(429).json({
            message: `Too many failed login attempts. Account temporarily locked for ${timeUntilUnblock} minutes.`
          });
        }
        const remainingAttempts = req.rateLimiter.getRemainingAttempts(req.clientIP);
        return res.status(401).json({
          message: `Invalid email or password. ${remainingAttempts} attempts remaining.`
        });
      }
      return res.status(401).json({ message: 'Invalid email or password' });
    }

    console.log('✅ Password match! Proceeding with login...');

    // Record successful login (clear failed attempts)
    if (req.rateLimiter && req.clientIP) {
      req.rateLimiter.recordSuccessfulLogin(req.clientIP);
    }

    console.log('💾 Updating last login...');
    // Update last login
    user.lastLogin = Date.now();
    await user.save();

    console.log('🔐 Generating JWT token...');
    // Generate JWT token
    const token = jwt.sign(
      { id: user._id, email: user.email, isAdmin: user.isAdmin || false },
      process.env.JWT_SECRET || 'fallback_secret_key',
      { expiresIn: '1d' }
    );

    // Track login analytics
    if (user.isEmailVerified) {
      try {
        const deviceInfo = detectDevice(req.headers['user-agent']);
        const sessionId = uuidv4();

        await Analytics.create({
          user: user._id,
          sessionId,
          event: 'login',
          eventData: {
            loginMethod: 'email',
            success: true
          },
          deviceInfo: {
            userAgent: req.headers['user-agent'],
            ...deviceInfo
          },
          location: {
            ipAddress: getClientIP(req)
          },
          userContext: {
            isAdmin: user.isAdmin || false,
            isActive: user.isActive || false,
            isVerified: user.isEmailVerified || false,
            userType: user.isAdmin ? 'admin' : 'user',
            accountAge: Math.floor((Date.now() - user.createdAt) / (1000 * 60 * 60 * 24))
          },
          sessionInfo: {
            isNewSession: true,
            entryPage: '/login'
          }
        });
      } catch (analyticsError) {
        console.error('Error tracking login analytics:', analyticsError);
      }
    }

    console.log('✅ Login successful! Sending response...');

    // Log login activity using ActivityService
    await ActivityService.logLogin(user._id, user.email, req);

    // Check if user needs to accept terms (first login after email verification)
    const requiresTermsAcceptance = !user.termsAccepted;

    // Return success with token and user info
    res.json({
      message: 'Login successful',
      token,
      requiresTermsAcceptance,
      user: {
        id: user._id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        dateOfBirth: user.dateOfBirth, // Include DOB for age calculation
        isAdmin: user.isAdmin || false
      }
    });
  } catch (error) {
    console.error('❌ Login error:', error);
    res.status(500).json({ message: 'Login failed. Please try again.' });
  }
};

// Logout endpoint
exports.logout = async (req, res) => {
  try {
    // Log logout activity using ActivityService
    if (req.user && req.user.id) {
      await ActivityService.logLogout(req.user.id, req);
    }

    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ message: 'Failed to logout' });
  }
};

exports.getProfile = async (req, res) => {
  try {
    // req.user is set by the auth middleware
    const user = await User.findById(req.user.id).select('-password');
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json(user);
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ message: 'Failed to get profile' });
  }
};

exports.updateProfile = async (req, res) => {
  try {
    const { firstName, lastName, dateOfBirth, gender, barangay } = req.body;

    // Find user and update
    const user = await User.findByIdAndUpdate(
      req.user.id,
      {
        firstName,
        lastName,
        dateOfBirth,
        gender,
        barangay,
        updatedAt: Date.now()
      },
      { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Track profile update analytics
    if (user.isEmailVerified) {
      try {
        const deviceInfo = detectDevice(req.headers['user-agent']);

        await Analytics.create({
          user: user._id,
          sessionId: req.headers['x-session-id'] || uuidv4(),
          event: 'profile_update',
          eventData: {
            fieldsUpdated: Object.keys(req.body),
            updateType: 'backend_profile_update'
          },
          deviceInfo: {
            userAgent: req.headers['user-agent'],
            ...deviceInfo
          },
          location: {
            ipAddress: getClientIP(req)
          },
          userContext: {
            isAdmin: user.isAdmin || false,
            isActive: user.isActive || false,
            isVerified: user.isEmailVerified || false,
            userType: user.isAdmin ? 'admin' : 'user',
            accountAge: Math.floor((Date.now() - user.createdAt) / (1000 * 60 * 60 * 24))
          }
        });
      } catch (analyticsError) {
        console.error('Error tracking profile update analytics:', analyticsError);
      }
    }

    // Log profile update activity using ActivityService
    await ActivityService.logProfileUpdate(req.user.id, { firstName, lastName, dateOfBirth, gender, barangay }, req);

    res.json({
      message: 'Profile updated successfully',
      user
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ message: 'Failed to update profile' });
  }
};

// Change password
exports.changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // Validate required fields
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        message: 'Current password and new password are required'
      });
    }

    // Validate new password strength
    if (newPassword.length < 6) {
      return res.status(400).json({
        message: 'New password must be at least 6 characters long'
      });
    }

    // Find user with password
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Verify current password
    const isCurrentPasswordValid = await user.comparePassword(currentPassword);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({ message: 'Current password is incorrect' });
    }

    // Update password (will be hashed by the pre-save hook)
    user.password = newPassword;
    user.updatedAt = Date.now();
    await user.save();

    res.json({
      message: 'Password changed successfully'
    });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({ message: 'Failed to change password' });
  }
};

exports.deleteAccount = async (req, res) => {
  try {
    const user = await User.findByIdAndDelete(req.user.id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({ message: 'Account deleted successfully' });
  } catch (error) {
    console.error('Delete account error:', error);
    res.status(500).json({ message: 'Failed to delete account' });
  }
};

// Update user dietary preferences
exports.updateDietaryPreferences = async (req, res) => {
  try {
    console.log('Updating dietary preferences for user:', req.user.id);
    console.log('Request body:', req.body);

    const user = await User.findById(req.user.id);
    if (!user) {
      console.log('User not found');
      return res.status(404).json({ message: "User not found" });
    }

    // Store old preferences for comparison
    const oldPreferences = user.dietaryPreferences ? JSON.parse(JSON.stringify(user.dietaryPreferences)) : {};

    // Update dietary preferences with all fields
    user.dietaryPreferences = {
      restrictions: req.body.restrictions || [],
      allergies: req.body.allergies || [],
      dislikedIngredients: req.body.dislikedIngredients || [],
      calorieTarget: req.body.calorieTarget || null,
      mealFrequency: req.body.mealFrequency || 3,
      macroTargets: req.body.macroTargets || {
        protein: null,
        carbs: null,
        fat: null
      }
    };

    console.log('Saving preferences:', user.dietaryPreferences);
    await user.save();

    // Check if dietary preferences have changed significantly
    const preferencesChanged = hasSignificantDietaryChanges(oldPreferences, user.dietaryPreferences);

    // Respond immediately to improve user experience
    console.log('Preferences saved successfully');
    res.json({
      success: true,
      dietaryPreferences: user.dietaryPreferences,
      message: 'Dietary preferences updated successfully',
      mealPlanUpdateScheduled: preferencesChanged
    });

    // Process meal plan updates asynchronously (don't wait for response)
    if (preferencesChanged) {
      console.log('Significant dietary changes detected, scheduling meal plan updates...');

      // Run meal plan updates in background without blocking the response
      setImmediate(async () => {
        try {
          const { updateFutureMealPlansForDietaryChanges } = require('../services/mealPlanUpdateService');
          const mealPlanUpdateResult = await updateFutureMealPlansForDietaryChanges(req.user.id, user.dietaryPreferences);
          console.log('Background meal plan update completed:', mealPlanUpdateResult);
        } catch (updateError) {
          console.error('Error in background meal plan update:', updateError);
        }
      });
    }

  } catch (error) {
    console.error('Error updating dietary preferences:', error);
    res.status(500).json({
      success: false,
      message: "Failed to update preferences",
      error: error.message
    });
  }
};

// Helper function to check if dietary preferences have changed significantly
function hasSignificantDietaryChanges(oldPrefs, newPrefs) {
  // Check if restrictions changed
  const oldRestrictions = new Set(oldPrefs.restrictions || []);
  const newRestrictions = new Set(newPrefs.restrictions || []);

  if (oldRestrictions.size !== newRestrictions.size) return true;
  for (let restriction of newRestrictions) {
    if (!oldRestrictions.has(restriction)) return true;
  }

  // Check if allergies changed
  const oldAllergies = new Set(oldPrefs.allergies || []);
  const newAllergies = new Set(newPrefs.allergies || []);

  if (oldAllergies.size !== newAllergies.size) return true;
  for (let allergy of newAllergies) {
    if (!oldAllergies.has(allergy)) return true;
  }

  // REMOVED: Disliked ingredients checking (simplified logic)

  return false;
}

// Get user dietary preferences
exports.getDietaryPreferences = async (req, res) => {
  try {
    console.log('Getting dietary preferences for user:', req.user.id);

    // Find the user
    const user = await User.findById(req.user.id).select('dietaryPreferences');

    if (!user) {
      console.log('User not found');
      return res.status(404).json({ message: 'User not found' });
    }

    console.log('Retrieved preferences:', user.dietaryPreferences);

    res.status(200).json({
      success: true,
      dietaryPreferences: user.dietaryPreferences || {}
    });
  } catch (error) {
    console.error('Error getting dietary preferences:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get dietary preferences',
      error: error.message
    });
  }
};

// Add meal to favorites
exports.addFavoriteMeal = async (req, res) => {
  try {
    console.log('Adding meal to favorites for user:', req.user.id);
    console.log('Meal data:', req.body);

    const mealData = req.body.meal || req.body;

    // Validate meal data
    if (!mealData.name) {
      console.log('Invalid meal data: missing name');
      return res.status(400).json({ message: 'Invalid meal data: name is required' });
    }

    // Find the user
    const user = await User.findById(req.user.id);

    if (!user) {
      console.log('User not found');
      return res.status(404).json({ message: 'User not found' });
    }

    // Initialize favoriteMeals if it doesn't exist
    if (!user.favoriteMeals) {
      user.favoriteMeals = [];
    }

    // Check if meal already exists in favorites
    const existingMealIndex = user.favoriteMeals.findIndex(meal => meal.name === mealData.name);

    if (existingMealIndex !== -1) {
      console.log('Meal already in favorites');
      return res.status(400).json({ message: 'Meal already in favorites' });
    }

    // Add to favorites
    user.favoriteMeals.push(mealData);
    console.log('Added meal to favorites');

    await user.save();
    console.log('User after save:', await User.findById(req.user.id));

    res.status(200).json({
      success: true,
      message: 'Meal added to favorites',
      favoriteMeals: user.favoriteMeals
    });
  } catch (error) {
    console.error('Error adding favorite meal:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add favorite meal',
      error: error.message
    });
  }
};

// Remove meal from favorites
exports.removeFavoriteMeal = async (req, res) => {
  try {
    const { mealId } = req.params;
    console.log(`Removing meal ${mealId} from favorites for user:`, req.user.id);

    // Find the user
    const user = await User.findById(req.user.id);

    if (!user) {
      console.log('User not found');
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if favoriteMeals exists
    if (!user.favoriteMeals || user.favoriteMeals.length === 0) {
      console.log('No favorite meals found');
      return res.status(404).json({ message: 'No favorite meals found' });
    }

    // Remove from favorites
    const initialLength = user.favoriteMeals.length;
    user.favoriteMeals = user.favoriteMeals.filter(meal => meal._id.toString() !== mealId);

    if (initialLength === user.favoriteMeals.length) {
      console.log('Meal not found in favorites');
      return res.status(404).json({ message: 'Meal not found in favorites' });
    }

    console.log('Removed meal from favorites');
    await user.save();

    res.status(200).json({
      success: true,
      message: 'Meal removed from favorites',
      favoriteMeals: user.favoriteMeals
    });
  } catch (error) {
    console.error('Error removing favorite meal:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove favorite meal',
      error: error.message
    });
  }
};

// Get favorite meals
exports.getFavoriteMeals = async (req, res) => {
  try {
    console.log('Getting favorite meals for user:', req.user.id);

    // Find the user
    const user = await User.findById(req.user.id).select('favoriteMeals');

    if (!user) {
      console.log('User not found');
      return res.status(404).json({ message: 'User not found' });
    }

    console.log(`Found ${user.favoriteMeals ? user.favoriteMeals.length : 0} favorite meals`);

    res.status(200).json({
      success: true,
      favoriteMeals: user.favoriteMeals || []
    });
  } catch (error) {
    console.error('Error getting favorite meals:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get favorite meals',
      error: error.message
    });
  }
};

// Add meal plan to favorites
exports.addFavoriteMealPlan = async (req, res) => {
  try {
    const mealPlanData = req.body;
    console.log('🔍 Backend: Received meal plan data:', JSON.stringify(mealPlanData, null, 2));
    console.log(`Adding meal plan "${mealPlanData.name}" to favorites for user:`, req.user.id);

    // Validate required fields
    if (!mealPlanData.name) {
      console.log('❌ Backend: Meal plan name is required');
      return res.status(400).json({ message: 'Meal plan name is required' });
    }

    // Find the user
    const user = await User.findById(req.user.id);

    if (!user) {
      console.log('User not found');
      return res.status(404).json({ message: 'User not found' });
    }

    // Initialize favoriteMealPlans if it doesn't exist
    if (!user.favoriteMealPlans) {
      user.favoriteMealPlans = [];
    }

    // Check if meal plan with same name already exists in favorites
    const existingFavorite = user.favoriteMealPlans.find(plan =>
      plan.name === mealPlanData.name
    );

    if (existingFavorite) {
      console.log('Meal plan with this name already in favorites');
      return res.status(400).json({ message: 'Meal plan with this name already in favorites' });
    }

    // Create a unique ID for this favorite meal plan
    const favoriteId = new Date().getTime().toString();

    // Add to favorite meal plans with complete data
    const favoritePlan = {
      _id: favoriteId,
      name: mealPlanData.name,
      startDate: mealPlanData.startDate,
      endDate: mealPlanData.endDate,
      totalMeals: mealPlanData.totalMeals || 0,
      totalDays: mealPlanData.totalDays || 0,
      totalCalories: mealPlanData.totalCalories || 0,
      totalBudget: mealPlanData.totalBudget || 0,
      meals: mealPlanData.meals || [],
      mealTimes: mealPlanData.mealTimes || {},
      mealTypes: mealPlanData.mealTypes || [],
      riceBowlsPerDay: mealPlanData.riceBowlsPerDay || {},
      createdAt: mealPlanData.createdAt || new Date().toISOString(),
      addedAt: new Date().toISOString(),
      isFavorite: true,
      source: mealPlanData.source || 'manual'
    };

    user.favoriteMealPlans.push(favoritePlan);

    console.log('Added meal plan to favorites with complete data');
    await user.save();

    res.status(200).json({
      success: true,
      message: 'Meal plan added to favorites',
      favoriteMealPlans: user.favoriteMealPlans
    });
  } catch (error) {
    console.error('Error adding favorite meal plan:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add favorite meal plan',
      error: error.message
    });
  }
};

// Get favorite meal plans
exports.getFavoriteMealPlans = async (req, res) => {
  try {
    console.log('Getting favorite meal plans for user:', req.user.id);

    // Find the user (no need to populate since we store complete data)
    const user = await User.findById(req.user.id)
      .select('favoriteMealPlans');

    if (!user) {
      console.log('User not found');
      return res.status(404).json({ message: 'User not found' });
    }

    console.log(`Found ${user.favoriteMealPlans ? user.favoriteMealPlans.length : 0} favorite meal plans`);

    res.status(200).json({
      success: true,
      favoriteMealPlans: user.favoriteMealPlans || []
    });
  } catch (error) {
    console.error('Error getting favorite meal plans:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get favorite meal plans',
      error: error.message
    });
  }
};

// Remove meal plan from favorites
exports.removeFavoriteMealPlan = async (req, res) => {
  try {
    const { planId } = req.params;
    console.log(`Removing favorite meal plan ${planId} for user:`, req.user.id);

    // Find the user
    const user = await User.findById(req.user.id);

    if (!user) {
      console.log('User not found');
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if favoriteMealPlans exists
    if (!user.favoriteMealPlans || user.favoriteMealPlans.length === 0) {
      console.log('No favorite meal plans found');
      return res.status(404).json({ message: 'No favorite meal plans found' });
    }

    // Remove the favorite meal plan
    const initialLength = user.favoriteMealPlans.length;
    user.favoriteMealPlans = user.favoriteMealPlans.filter(
      favorite => favorite._id.toString() !== planId
    );

    if (initialLength === user.favoriteMealPlans.length) {
      console.log('Favorite meal plan not found');
      return res.status(404).json({ message: 'Favorite meal plan not found' });
    }

    console.log('Removed favorite meal plan');
    await user.save();

    res.status(200).json({
      success: true,
      message: 'Favorite meal plan removed successfully',
      favoriteMealPlans: user.favoriteMealPlans
    });
  } catch (error) {
    console.error('Error removing favorite meal plan:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove favorite meal plan',
      error: error.message
    });
  }
};

// Add meal to recently viewed
exports.addRecentlyViewedMeal = async (req, res) => {
  try {
    console.log('=== ADD RECENTLY VIEWED MEAL ===');
    console.log('Request body:', JSON.stringify(req.body, null, 2));
    console.log('User ID:', req.user.id);

    const rawMealData = req.body.meal || req.body;
    console.log('Raw meal data:', JSON.stringify(rawMealData, null, 2));

    if (!rawMealData.name) {
      console.log('ERROR: No meal name provided');
      return res.status(400).json({ message: 'Invalid meal data: name is required' });
    }

    // Helper function to convert dietType object to array of strings
    const processDietType = (dietType) => {
      if (!dietType) return [];

      // If it's already an array of strings, return it
      if (Array.isArray(dietType) && dietType.every(item => typeof item === 'string')) {
        return dietType;
      }

      // If it's a string that looks like JSON, try to parse it
      if (typeof dietType === 'string') {
        try {
          const parsed = JSON.parse(dietType);
          if (Array.isArray(parsed)) {
            return processDietType(parsed);
          }
          return processDietType([parsed]);
        } catch (e) {
          // If parsing fails, treat as a single string
          return [dietType];
        }
      }

      // If it's an array with objects, convert to strings
      if (Array.isArray(dietType)) {
        return dietType.map(item => {
          if (typeof item === 'string') return item;
          if (typeof item === 'object' && item !== null) {
            // Convert object with boolean flags to array of diet types
            const dietTypes = [];
            if (item.isVegetarian) dietTypes.push('Vegetarian');
            if (item.isVegan) dietTypes.push('Vegan');
            if (item.isDairyFree) dietTypes.push('Dairy-Free');
            if (item.isGlutenFree) dietTypes.push('Gluten-Free');
            if (item.isNutFree) dietTypes.push('Nut-Free');
            if (item.isLowCarb) dietTypes.push('Low-Carb');
            if (item.isHalal) dietTypes.push('Halal');
            if (item.isPescatarian) dietTypes.push('Pescatarian');
            if (item.isKeto) dietTypes.push('Keto');
            return dietTypes.join(', ');
          }
          return String(item);
        }).filter(Boolean);
      }

      // If it's a single object, convert it
      if (typeof dietType === 'object' && dietType !== null) {
        const dietTypes = [];
        if (dietType.isVegetarian) dietTypes.push('Vegetarian');
        if (dietType.isVegan) dietTypes.push('Vegan');
        if (dietType.isDairyFree) dietTypes.push('Dairy-Free');
        if (dietType.isGlutenFree) dietTypes.push('Gluten-Free');
        if (dietType.isNutFree) dietTypes.push('Nut-Free');
        if (dietType.isLowCarb) dietTypes.push('Low-Carb');
        if (dietType.isHalal) dietTypes.push('Halal');
        if (dietType.isPescatarian) dietTypes.push('Pescatarian');
        if (dietType.isKeto) dietTypes.push('Keto');
        return dietTypes;
      }

      return [String(dietType)];
    };

    // Sanitize meal data to ensure proper types
    const mealData = {
      id: rawMealData.id || rawMealData._id,
      _id: rawMealData._id || rawMealData.id,
      name: rawMealData.name,
      mealType: Array.isArray(rawMealData.mealType) ? rawMealData.mealType : [rawMealData.mealType].filter(Boolean),
      category: Array.isArray(rawMealData.category) ? rawMealData.category : [rawMealData.category].filter(Boolean),
      dietaryTags: Array.isArray(rawMealData.dietaryTags) ? rawMealData.dietaryTags : [rawMealData.dietaryTags].filter(Boolean),
      dietType: processDietType(rawMealData.dietType),
      rating: Array.isArray(rawMealData.rating) ? rawMealData.rating : [rawMealData.rating].filter(Boolean),
      calories: Number(rawMealData.calories) || 0,
      protein: Number(rawMealData.protein) || 0,
      carbs: Number(rawMealData.carbs) || 0,
      fat: Number(rawMealData.fat) || 0,
      image: rawMealData.image || '',
      description: rawMealData.description || '',
      ingredients: Array.isArray(rawMealData.ingredients) ? rawMealData.ingredients : [rawMealData.ingredients].filter(Boolean),
      instructions: Array.isArray(rawMealData.instructions) ? rawMealData.instructions : [rawMealData.instructions].filter(Boolean),
      priceRange: rawMealData.priceRange || 'Low',
      viewedAt: new Date()
    };

    console.log('Sanitized meal data:', JSON.stringify(mealData, null, 2));

    const user = await User.findById(req.user.id);
    if (!user) {
      console.log('ERROR: User not found');
      return res.status(404).json({ message: 'User not found' });
    }

    console.log('Current recently viewed meals count:', user.recentlyViewedMeals.length);

    // Remove if already exists (by name or id)
    user.recentlyViewedMeals = user.recentlyViewedMeals.filter(
      meal => meal.name !== mealData.name
    );

    // Add to the beginning
    user.recentlyViewedMeals.unshift(mealData);

    // Limit to last 10 meals (or any number you want)
    if (user.recentlyViewedMeals.length > 10) {
      user.recentlyViewedMeals = user.recentlyViewedMeals.slice(0, 10);
    }

    console.log('New recently viewed meals count:', user.recentlyViewedMeals.length);
    console.log('Recently viewed meals:', JSON.stringify(user.recentlyViewedMeals.map(m => m.name), null, 2));

    await user.save();
    console.log('User saved successfully');

    res.status(200).json({
      success: true,
      message: 'Meal added to recently viewed',
      recentlyViewedMeals: user.recentlyViewedMeals
    });
  } catch (error) {
    console.error('ERROR in addRecentlyViewedMeal:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add recently viewed meal',
      error: error.message
    });
  }
};
//undo if not working
// Get recently viewed meals
exports.getRecentlyViewedMeals = async (req, res) => {
  try {
    console.log('=== GET RECENTLY VIEWED MEALS ===');
    console.log('User ID:', req.user.id);

    const user = await User.findById(req.user.id);
    if (!user) {
      console.log('ERROR: User not found');
      return res.status(404).json({ message: "User not found" });
    }

    const recentlyViewedMeals = user.recentlyViewedMeals || [];
    console.log('Recently viewed meals count:', recentlyViewedMeals.length);
    console.log('Recently viewed meals:', JSON.stringify(recentlyViewedMeals.map(m => ({ name: m.name, id: m.id || m._id })), null, 2));

    res.json({ recentlyViewedMeals });
  } catch (error) {
    console.error('ERROR in getRecentlyViewedMeals:', error);
    res.status(500).json({ message: "Failed to fetch recently viewed meals", error: error.message });
  }
};

// Add meal to recently added to meal plans
exports.addRecentlyAddedToMealPlan = async (req, res) => {
  try {
    const { meal, addedToDate, addedToMealType } = req.body;

    if (!meal || !meal.name) {
      return res.status(400).json({ message: 'Invalid meal data: name is required' });
    }

    if (!addedToDate || !addedToMealType) {
      return res.status(400).json({ message: 'addedToDate and addedToMealType are required' });
    }

    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Remove if already exists (by name)
    user.recentlyAddedToMealPlans = user.recentlyAddedToMealPlans.filter(
      item => item.name !== meal.name
    );

    // Add to the beginning with additional metadata
    user.recentlyAddedToMealPlans.unshift({
      ...meal,
      addedAt: new Date(),
      addedToDate,
      addedToMealType
    });

    // Limit to last 15 meals
    if (user.recentlyAddedToMealPlans.length > 15) {
      user.recentlyAddedToMealPlans = user.recentlyAddedToMealPlans.slice(0, 15);
    }

    await user.save();

    res.status(200).json({
      success: true,
      message: 'Meal added to recently added to meal plans',
      recentlyAddedToMealPlans: user.recentlyAddedToMealPlans
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to add meal to recently added to meal plans',
      error: error.message
    });
  }
};

// Get recently added to meal plans
exports.getRecentlyAddedToMealPlans = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user) return res.status(404).json({ message: "User not found" });
    res.json({ recentlyAddedToMealPlans: user.recentlyAddedToMealPlans || [] });
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch recently added to meal plans", error: error.message });
  }
};

// Get meals from saved meal plans for history
exports.getMealsFromSavedPlans = async (req, res) => {
  try {
    const MealPlan = require('../models/MealPlan');

    // Get all saved meal plans for the user (non-template plans)
    const savedPlans = await MealPlan.find({
      user: req.user.id,
      isTemplate: false
    }).sort({ createdAt: -1 }).limit(50); // Get last 50 plans

    const mealsFromPlans = [];

    // Extract all meals from saved plans
    savedPlans.forEach(plan => {
      const mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];

      mealTypes.forEach(mealType => {
        if (plan[mealType] && plan[mealType].length > 0) {
          plan[mealType].forEach(meal => {
            mealsFromPlans.push({
              ...meal.toObject(),
              addedAt: plan.createdAt,
              addedToDate: plan.date,
              addedToMealType: mealType,
              planName: plan.templateName || 'Meal Plan'
            });
          });
        }
      });
    });

    // Sort by creation date (newest first) and limit to 15
    const sortedMeals = mealsFromPlans
      .sort((a, b) => new Date(b.addedAt) - new Date(a.addedAt))
      .slice(0, 15);

    res.json({ mealsFromSavedPlans: sortedMeals });
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch meals from saved plans", error: error.message });
  }
};

// Add family member
exports.addFamilyMember = async (req, res) => {
  try {
    console.log('🔄 ADD FAMILY MEMBER ENDPOINT CALLED');
    console.log('📦 Request body:', JSON.stringify(req.body, null, 2));

    const user = await User.findById(req.user.id);
    if (!user) return res.status(404).json({ message: "User not found" });

    // Ensure proper data structure
    const memberData = {
      name: req.body.name,
      dateOfBirth: req.body.dateOfBirth || null,
      dietaryPreferences: {
        restrictions: req.body.dietaryPreferences?.restrictions || [],
        allergies: req.body.dietaryPreferences?.allergies || [],
        dislikedIngredients: req.body.dietaryPreferences?.dislikedIngredients || [],
        calorieTarget: req.body.dietaryPreferences?.calorieTarget || null,
        macroTargets: {
          protein: req.body.dietaryPreferences?.macroTargets?.protein || null,
          carbs: req.body.dietaryPreferences?.macroTargets?.carbs || null,
          fat: req.body.dietaryPreferences?.macroTargets?.fat || null
        },
        mealFrequency: req.body.dietaryPreferences?.mealFrequency || 3
      }
    };

    console.log('✅ Processed member data:', JSON.stringify(memberData, null, 2));

    user.familyMembers.push(memberData);
    await user.save();

    console.log('✅ Family member added successfully');
    res.status(201).json({ success: true, familyMembers: user.familyMembers });
  } catch (error) {
    console.error('❌ Error adding family member:', error);
    res.status(500).json({ message: "Failed to add family member", error: error.message });
  }
};

exports.removeFamilyMember = async (req, res) => {
  try {
    const { memberId } = req.params;
    const user = await User.findById(req.user.id);
    if (!user) return res.status(404).json({ message: "User not found" });

    user.familyMembers = user.familyMembers.filter(
      m => m._id.toString() !== memberId
    );
    await user.save();
    res.json({ success: true, familyMembers: user.familyMembers });
  } catch (error) {
    res.status(500).json({ message: "Failed to remove family member.", error: error.message });
  }
};

// Update family member
exports.updateFamilyMember = async (req, res) => {
  try {
    console.log('🔄 UPDATE FAMILY MEMBER ENDPOINT CALLED');
    console.log('📍 Route: PUT /api/users/family-members/:memberId');
    console.log('👤 User ID:', req.user?.id);
    console.log('🆔 Member ID from params:', req.params.memberId);
    console.log('📦 Request body:', JSON.stringify(req.body, null, 2));
    console.log('🔗 Full URL:', req.originalUrl);

    const { memberId } = req.params;
    const updateData = req.body;

    console.log('Updating family member:', memberId, 'with data:', updateData);

    const user = await User.findById(req.user.id);
    if (!user) return res.status(404).json({ message: "User not found" });

    // Find the family member to update
    const memberIndex = user.familyMembers.findIndex(
      m => m._id.toString() === memberId
    );

    if (memberIndex === -1) {
      return res.status(404).json({ message: "Family member not found" });
    }

    // Update the family member data
    const currentMember = user.familyMembers[memberIndex];

    // Update basic info
    if (updateData.name) currentMember.name = updateData.name;
    if (updateData.dateOfBirth) currentMember.dateOfBirth = updateData.dateOfBirth;

    // Update dietary preferences
    if (updateData.dietaryPreferences) {
      console.log('🔍 Current member disliked ingredients:', currentMember.dietaryPreferences?.dislikedIngredients);
      console.log('🔍 Update data disliked ingredients:', updateData.dietaryPreferences.dislikedIngredients);
      // Ensure macroTargets is always a proper object
      const macroTargets = updateData.dietaryPreferences.macroTargets ||
                          currentMember.dietaryPreferences?.macroTargets ||
                          {};

      // Ensure macroTargets has the required structure
      const validMacroTargets = {
        protein: macroTargets.protein || null,
        carbs: macroTargets.carbs || null,
        fat: macroTargets.fat || null
      };

      currentMember.dietaryPreferences = {
        restrictions: updateData.dietaryPreferences.restrictions !== undefined ? updateData.dietaryPreferences.restrictions : (currentMember.dietaryPreferences?.restrictions || []),
        allergies: updateData.dietaryPreferences.allergies !== undefined ? updateData.dietaryPreferences.allergies : (currentMember.dietaryPreferences?.allergies || []),
        dislikedIngredients: updateData.dietaryPreferences.dislikedIngredients !== undefined ? updateData.dietaryPreferences.dislikedIngredients : (currentMember.dietaryPreferences?.dislikedIngredients || []),
        calorieTarget: updateData.dietaryPreferences.calorieTarget !== undefined ? updateData.dietaryPreferences.calorieTarget : (currentMember.dietaryPreferences?.calorieTarget || null),
        macroTargets: validMacroTargets,
        mealFrequency: updateData.dietaryPreferences.mealFrequency !== undefined ? updateData.dietaryPreferences.mealFrequency : (currentMember.dietaryPreferences?.mealFrequency || 3)
      };

      console.log('✅ Dietary preferences updated:', JSON.stringify(currentMember.dietaryPreferences, null, 2));
    }

    // Save the updated user
    await user.save();

    console.log('Family member updated successfully');
    res.json({ success: true, familyMembers: user.familyMembers });
  } catch (error) {
    console.error('Error updating family member:', error);
    res.status(500).json({ message: "Failed to update family member", error: error.message });
  }
};

// Email verification
exports.verifyEmail = async (req, res) => {
  try {
    const { token } = req.query;

    if (!token) {
      return res.status(400).json({ message: 'Verification token is required' });
    }

    // Find user with this verification token
    const user = await User.findOne({
      emailVerificationToken: token,
      emailVerificationExpires: { $gt: Date.now() }
    });

    if (!user) {
      return res.status(400).json({
        message: 'Invalid or expired verification token. Please request a new verification email.'
      });
    }

    // Verify the email
    user.isEmailVerified = true;
    user.emailVerificationToken = undefined;
    user.emailVerificationExpires = undefined;
    await user.save();

    // Send welcome email
    await emailService.sendWelcomeEmail(user.email, user.username || user.firstName);

    res.json({
      message: 'Email verified successfully! You can now log in.',
      emailVerified: true
    });
  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({ message: 'Email verification failed. Please try again.' });
  }
};

// Resend verification email
exports.resendVerificationEmail = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: 'Email is required' });
    }

    // Find user by email
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if already verified
    if (user.isEmailVerified) {
      return res.status(400).json({ message: 'Email is already verified' });
    }

    // Generate new verification token
    const verificationToken = emailService.generateVerificationToken();
    const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    user.emailVerificationToken = verificationToken;
    user.emailVerificationExpires = verificationExpires;
    await user.save();

    // Send verification email
    const emailSent = await emailService.sendVerificationEmail(
      user.email,
      user.username || user.firstName,
      verificationToken
    );

    if (!emailSent) {
      return res.status(500).json({ message: 'Failed to send verification email' });
    }

    res.json({
      message: 'Verification email sent successfully. Please check your email.',
      emailSent: true
    });
  } catch (error) {
    console.error('Resend verification email error:', error);
    res.status(500).json({ message: 'Failed to resend verification email' });
  }
};

// Request password reset
exports.requestPasswordReset = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: 'Email is required' });
    }

    // Find user by email
    const user = await User.findOne({ email });
    if (!user) {
      // Don't reveal if user exists or not for security
      return res.json({
        message: 'If an account with that email exists, a password reset link has been sent.'
      });
    }

    // Generate password reset token
    const resetToken = emailService.generatePasswordResetToken();
    const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

    user.passwordResetToken = resetToken;
    user.passwordResetExpires = resetExpires;
    await user.save();

    // Send password reset email
    const emailSent = await emailService.sendPasswordResetEmail(
      user.email,
      user.username || user.firstName,
      resetToken
    );

    if (!emailSent) {
      console.error('Failed to send password reset email');
    }

    res.json({
      message: 'If an account with that email exists, a password reset link has been sent.'
    });
  } catch (error) {
    console.error('Request password reset error:', error);
    res.status(500).json({ message: 'Failed to process password reset request' });
  }
};

// Reset password
exports.resetPassword = async (req, res) => {
  try {
    const { token, newPassword } = req.body;

    if (!token || !newPassword) {
      return res.status(400).json({ message: 'Token and new password are required' });
    }

    // Validate password strength
    if (newPassword.length < 6) {
      return res.status(400).json({
        message: 'Password must be at least 6 characters long'
      });
    }

    // Find user with valid reset token
    const user = await User.findOne({
      passwordResetToken: token,
      passwordResetExpires: { $gt: Date.now() }
    });

    if (!user) {
      return res.status(400).json({
        message: 'Invalid or expired password reset token'
      });
    }

    // Update password
    user.password = newPassword;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    user.updatedAt = Date.now();
    await user.save();

    res.json({
      message: 'Password reset successfully. You can now log in with your new password.'
    });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({ message: 'Failed to reset password' });
  }
};

// Verify OTP
exports.verifyOTP = async (req, res) => {
  try {
    const { email, otp } = req.body;

    if (!email || !otp) {
      return res.status(400).json({ message: 'Email and OTP are required' });
    }

    // Find user with this email and valid OTP
    const user = await User.findOne({
      email: email.toLowerCase(),
      emailOTP: otp,
      emailOTPExpires: { $gt: Date.now() }
    });

    if (!user) {
      return res.status(400).json({
        message: 'Invalid or expired OTP. Please request a new verification code.'
      });
    }

    // Verify the email
    user.isEmailVerified = true;
    user.emailOTP = undefined;
    user.emailOTPExpires = undefined;
    user.emailVerificationToken = undefined;
    user.emailVerificationExpires = undefined;
    await user.save();

    // Generate JWT token for the verified user
    const token = jwt.sign(
      { id: user._id, email: user.email, isAdmin: user.isAdmin || false },
      process.env.JWT_SECRET || 'fallback_secret_key',
      { expiresIn: '1d' }
    );

    // Send welcome email
    await emailService.sendWelcomeEmail(user.email, user.username || user.firstName);

    res.json({
      message: 'Email verified successfully! Welcome to PanlasApp!',
      token,
      user: {
        id: user._id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        isAdmin: user.isAdmin || false,
        isEmailVerified: true
      }
    });
  } catch (error) {
    console.error('OTP verification error:', error);
    res.status(500).json({ message: 'OTP verification failed. Please try again.' });
  }
};

// Resend OTP
exports.resendOTP = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: 'Email is required' });
    }

    // Find user by email
    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if already verified
    if (user.isEmailVerified) {
      return res.status(400).json({ message: 'Email is already verified' });
    }

    // Generate new OTP
    const emailOTP = emailService.generateOTP();
    const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    user.emailOTP = emailOTP;
    user.emailOTPExpires = otpExpires;
    await user.save();

    // Send verification email with new OTP
    const emailSent = await emailService.sendVerificationEmail(
      user.email,
      user.username || user.firstName,
      user.emailVerificationToken,
      emailOTP
    );

    if (!emailSent) {
      return res.status(500).json({ message: 'Failed to send verification email' });
    }

    res.json({
      message: 'New verification code sent successfully. Please check your email.',
      emailSent: true
    });
  } catch (error) {
    console.error('Resend OTP error:', error);
    res.status(500).json({ message: 'Failed to resend verification code' });
  }
};

// Get family members
exports.getFamilyMembers = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('familyMembers');
    if (!user) return res.status(404).json({ message: "User not found" });
    res.json({ familyMembers: user.familyMembers || [] });
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch family members", error: error.message });
  }
};

// Logout user and track analytics
exports.logout = async (req, res) => {
  try {
    // Track logout analytics
    if (req.user) {
      try {
        const user = await User.findById(req.user.id);
        if (user && user.isEmailVerified) {
          const deviceInfo = detectDevice(req.headers['user-agent']);

          await Analytics.create({
            user: user._id,
            sessionId: req.headers['x-session-id'] || uuidv4(),
            event: 'logout',
            eventData: {
              logoutMethod: 'manual',
              success: true
            },
            deviceInfo: {
              userAgent: req.headers['user-agent'],
              ...deviceInfo
            },
            location: {
              ipAddress: getClientIP(req)
            },
            userContext: {
              isAdmin: user.isAdmin || false,
              isActive: user.isActive || false,
              isVerified: user.isEmailVerified || false,
              userType: user.isAdmin ? 'admin' : 'user',
              accountAge: Math.floor((Date.now() - user.createdAt) / (1000 * 60 * 60 * 24))
            },
            sessionInfo: {
              exitPage: req.headers.referer || '/logout'
            }
          });
        }
      } catch (analyticsError) {
        console.error('Error tracking logout analytics:', analyticsError);
      }
    }

    res.json({
      success: true,
      message: 'Logout successful'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Logout failed'
    });
  }
};

// Get terms acceptance status
exports.getTermsStatus = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('termsAccepted termsAcceptedAt termsVersion');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      termsAccepted: user.termsAccepted || false,
      termsAcceptedAt: user.termsAcceptedAt,
      termsVersion: user.termsVersion || '1.0',
      currentTermsVersion: '1.0' // This can be updated when terms change
    });
  } catch (error) {
    console.error('Get terms status error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Accept terms and conditions
exports.acceptTerms = async (req, res) => {
  try {
    console.log('✅ acceptTerms controller called');
    console.log('📝 req.updatedTerms:', req.updatedTerms);

    // The updateTermsAcceptance middleware has already updated the user
    // req.updatedTerms contains the updated terms info

    const response = {
      message: 'Terms and conditions accepted successfully',
      termsAccepted: req.updatedTerms.accepted,
      termsAcceptedAt: req.updatedTerms.acceptedAt,
      termsVersion: req.updatedTerms.version
    };

    console.log('📤 Sending response:', response);
    res.json(response);
  } catch (error) {
    console.error('Accept terms error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};
