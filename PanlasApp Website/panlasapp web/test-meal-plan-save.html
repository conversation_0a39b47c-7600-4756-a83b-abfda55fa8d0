<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Meal Plan Save</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 Meal Plan Save Test</h1>
    
    <div class="test-section info">
        <h3>Instructions:</h3>
        <ol>
            <li>Make sure your backend server is running on localhost:5000</li>
            <li>Make sure you're logged in (check localStorage for token)</li>
            <li>Click the test buttons below to verify the save functionality</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>🔐 Authentication Status</h3>
        <button onclick="checkAuth()">Check Authentication</button>
        <div id="auth-result"></div>
    </div>

    <div class="test-section">
        <h3>📋 Test Meal Plan Save</h3>
        <button onclick="testSaveMealPlan()">Test Save Meal Plan</button>
        <div id="save-result"></div>
    </div>

    <div class="test-section">
        <h3>🔍 Test Get Meal Plan</h3>
        <button onclick="testGetMealPlan()">Test Get Meal Plan for Today</button>
        <div id="get-result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000/api';

        function getToken() {
            return localStorage.getItem('token');
        }

        async function apiRequest(endpoint, options = {}) {
            const token = getToken();
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };
            
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
            
            const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                ...options,
                headers
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(`${response.status}: ${data.message || 'API request failed'}`);
            }
            
            return data;
        }

        async function checkAuth() {
            const resultDiv = document.getElementById('auth-result');
            try {
                const token = getToken();
                if (!token) {
                    resultDiv.innerHTML = '<div class="error">❌ No token found. Please log in first.</div>';
                    return;
                }

                const response = await apiRequest('/users/auth-status');
                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ Authentication successful<br>
                        User: ${response.user?.firstName || 'Unknown'}<br>
                        Token length: ${token.length}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Auth failed: ${error.message}</div>`;
            }
        }

        async function testSaveMealPlan() {
            const resultDiv = document.getElementById('save-result');
            try {
                const today = new Date().toISOString().split('T')[0];
                
                const testMealPlanData = {
                    name: `Test Meal Plan for ${today}`,
                    startDate: today,
                    endDate: today,
                    dietaryPreference: 'all',
                    riceBowls: 1,
                    riceBowlsPerDay: { [today]: 1 },
                    meals: [
                        {
                            date: today,
                            mealType: 'breakfast',
                            mealData: {
                                name: 'Test Breakfast',
                                calories: 300,
                                category: ['Breakfast'],
                                description: 'A test breakfast meal',
                                image: '',
                                ingredients: ['Test ingredient 1', 'Test ingredient 2'],
                                instructions: ['Test instruction 1'],
                                dietaryTags: [],
                                rating: 4,
                                instanceId: `test-breakfast-${Date.now()}`
                            }
                        }
                    ],
                    mealTimes: {
                        breakfast: '08:00',
                        lunch: '12:00',
                        dinner: '18:00',
                        snack: '15:00'
                    }
                };

                console.log('Sending test meal plan data:', testMealPlanData);
                
                const response = await apiRequest('/meal-plans/save', {
                    method: 'POST',
                    body: JSON.stringify(testMealPlanData)
                });

                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ Save successful!<br>
                        <pre>${JSON.stringify(response, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ Save failed: ${error.message}<br>
                        <pre>${error.stack || error}</pre>
                    </div>
                `;
            }
        }

        async function testGetMealPlan() {
            const resultDiv = document.getElementById('get-result');
            try {
                const today = new Date().toISOString().split('T')[0];
                
                const response = await apiRequest(`/meal-plans/${today}`);

                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ Get successful!<br>
                        <pre>${JSON.stringify(response, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                if (error.message.includes('404')) {
                    resultDiv.innerHTML = `
                        <div class="info">
                            ℹ️ No meal plan found for today (this is normal if you haven't created one yet)<br>
                            Error: ${error.message}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ Get failed: ${error.message}<br>
                            <pre>${error.stack || error}</pre>
                        </div>
                    `;
                }
            }
        }

        // Auto-check auth on page load
        window.onload = function() {
            checkAuth();
        };
    </script>
</body>
</html>
