<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>CORS Configuration Test</h1>
    <p>This page tests CORS configuration for your PanlasApp backend.</p>
    
    <div>
        <button onclick="testCORS()">Test CORS</button>
        <button onclick="testAPI()">Test API Endpoint</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script>
        const API_BASE_URL = 'http://localhost:5000/api';
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testCORS() {
            addResult('🔄 Testing CORS configuration...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/cors-test`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('✅ CORS test successful!', 'success');
                    addResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                } else {
                    addResult(`❌ CORS test failed with status: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ CORS test failed: ${error.message}`, 'error');
                addResult('This usually indicates a CORS policy violation.', 'error');
            }
        }
        
        async function testAPI() {
            addResult('🔄 Testing API endpoint...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/meals/filipino`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('✅ API test successful!', 'success');
                    addResult(`Received ${data.length} meals`, 'info');
                } else {
                    addResult(`❌ API test failed with status: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ API test failed: ${error.message}`, 'error');
            }
        }
        
        // Auto-run tests when page loads
        window.onload = function() {
            addResult('🚀 CORS Test Page Loaded', 'info');
            addResult(`Testing against: ${API_BASE_URL}`, 'info');
        };
    </script>
</body>
</html>
