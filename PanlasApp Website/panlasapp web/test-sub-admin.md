# Sub Admin Functionality Test Guide

This document outlines how to test the newly implemented sub admin functionality.

## Overview

Sub admins have been implemented with the following characteristics:
- **Read-only access** to the admin dashboard (cannot see overview, user management, analytics)
- **Full CRUD access** to meal management
- **Full CRUD access** to feedback management

## Test Steps

### 1. Create a Sub Admin User

1. Log in as a super admin or admin
2. Go to Admin Dashboard → User Management
3. Find a regular user and click "Make Admin"
4. In the role selection modal, choose "Sub Admin"
5. Confirm the action

### 2. Test Sub Admin Login

1. Log out from the admin account
2. Log in with the sub admin credentials
3. Navigate to `/admin` route

### 3. Verify Dashboard Access

**Expected Behavior:**
- Sub admin should see only "Meal Management" and "Feedback Management" tabs
- Should NOT see: Overview, User Management, Analytics, Geolocation Analytics tabs
- Default tab should be "Meal Management" (if they have meal_management permission)

### 4. Test Meal Management Access

**Sub admin should be able to:**
- View all meals (GET /api/meals)
- Create new meals (POST /api/meals)
- Update existing meals (PUT /api/meals/:id)
- Delete meals (DELETE /api/meals/:id)

**Test URLs:**
- Direct access to meal management: `/meals` (if meal management page exists)
- API endpoints should work with sub admin token

### 5. Test Feedback Management Access

**Sub admin should be able to:**
- View all feedback (GET /api/feedback/all)
- Update feedback status (PUT /api/feedback/:id/status)
- Add admin responses (POST /api/feedback/:id/response)
- Delete feedback (DELETE /api/feedback/:id)

**Test URLs:**
- Direct access to feedback management: `/admin/feedback`
- API endpoints should work with sub admin token

### 6. Test Restricted Access

**Sub admin should NOT be able to:**
- Access user management functions
- View system analytics
- See overview statistics
- Manage other admin users

**Test by trying to access:**
- User management API endpoints (should return 403)
- Analytics endpoints (should return 403)
- System overview endpoints (should return 403)

## Database Verification

Check the Admin collection in MongoDB:

```javascript
// Find sub admin records
db.admins.find({ role: "sub_admin" })

// Verify permissions
db.admins.find({ 
  role: "sub_admin",
  permissions: { $all: ["meal_management", "feedback_management"] }
})
```

## API Testing with Postman/curl

### Get Sub Admin Token
```bash
curl -X POST http://localhost:5000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'
```

### Test Meal Management (Should Work)
```bash
# Get meals
curl -X GET http://localhost:5000/api/meals \
  -H "x-auth-token: YOUR_SUB_ADMIN_TOKEN"

# Create meal
curl -X POST http://localhost:5000/api/meals \
  -H "x-auth-token: YOUR_SUB_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name": "Test Meal", "description": "Test Description"}'
```

### Test Feedback Management (Should Work)
```bash
# Get all feedback
curl -X GET http://localhost:5000/api/feedback/all \
  -H "x-auth-token: YOUR_SUB_ADMIN_TOKEN"
```

### Test Restricted Endpoints (Should Return 403)
```bash
# Try to get users (should fail)
curl -X GET http://localhost:5000/api/admin/users \
  -H "x-auth-token: YOUR_SUB_ADMIN_TOKEN"

# Try to get analytics (should fail)
curl -X GET http://localhost:5000/api/admin/analytics/geolocation \
  -H "x-auth-token: YOUR_SUB_ADMIN_TOKEN"
```

## Expected Results

### ✅ Should Work
- Sub admin login
- Access to admin dashboard with limited tabs
- Full meal CRUD operations
- Full feedback CRUD operations
- Meal and feedback API endpoints

### ❌ Should Fail (403 Forbidden)
- User management operations
- Analytics access
- System overview access
- Admin user creation/modification

## Troubleshooting

If tests fail, check:

1. **Database**: Verify admin record exists with correct role and permissions
2. **Middleware**: Ensure `adminOrSubAdminAuth` and permission middlewares are applied
3. **Frontend**: Check browser console for permission-related errors
4. **Backend**: Check server logs for authentication/authorization errors

## Role Hierarchy

```
super_admin (level 4) - All permissions
admin (level 3) - Most permissions
sub_admin (level 2) - Limited permissions (meal + feedback only)
moderator (level 1) - Content management only
```
