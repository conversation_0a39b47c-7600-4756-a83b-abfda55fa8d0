# PanlasApp Linode Deployment Guide

This guide will help you deploy the PanlasApp to your Linode server with the domain `panlasapp.food`.

## Server Details
- **IP Address**: ***************
- **Domain**: panlasapp.food
- **User**: umer
- **Password**: GDHee3voQErOJuf

## Prerequisites

### On Your Local Machine
- Git installed
- SSH access to the Linode server
- Domain DNS configured to point to the server IP

### On the Linode Server
- Ubuntu 20.04+ or similar Linux distribution
- Node.js 18+ installed
- MongoDB installed and running
- Nginx installed
- PM2 process manager installed
- SSL certificate (Let's Encrypt recommended)

## Quick Deployment

1. **Clone this repository on the server:**
   ```bash
   git clone <your-repo-url> /var/www/panlasapp
   cd /var/www/panlasapp
   ```

2. **Run the deployment script:**
   ```bash
   chmod +x deployment/linode/deploy.sh
   sudo ./deployment/linode/deploy.sh
   ```

3. **Configure environment variables:**
   ```bash
   sudo nano /var/www/panlasapp/PanlasApp\ Website/panlasapp\ web/.env
   sudo nano /var/www/panlasapp/PanlasApp\ Website/panlasapp\ web/meal-planner-backend/.env
   ```

4. **Start the services:**
   ```bash
   sudo systemctl start nginx
   sudo systemctl enable nginx
   pm2 start /var/www/panlasapp/deployment/linode/ecosystem.config.js
   pm2 save
   pm2 startup
   ```

## Manual Deployment Steps

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install MongoDB
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org

# Install Nginx
sudo apt install nginx -y

# Install PM2
sudo npm install -g pm2

# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod
```

### 2. Application Setup

```bash
# Create application directory
sudo mkdir -p /var/www/panlasapp
sudo chown -R $USER:$USER /var/www/panlasapp

# Clone repository
git clone <your-repo-url> /var/www/panlasapp
cd /var/www/panlasapp

# Install backend dependencies
cd "PanlasApp Website/panlasapp web/meal-planner-backend"
npm install

# Install frontend dependencies and build
cd "../"
npm install
npm run build
```

### 3. Environment Configuration

Copy the environment files from the deployment directory:
```bash
cp deployment/linode/.env.production "PanlasApp Website/panlasapp web/.env"
cp deployment/linode/.env.backend.production "PanlasApp Website/panlasapp web/meal-planner-backend/.env"
```

### 4. Nginx Configuration

```bash
sudo cp deployment/linode/nginx.conf /etc/nginx/sites-available/panlasapp.food
sudo ln -s /etc/nginx/sites-available/panlasapp.food /etc/nginx/sites-enabled/
sudo rm /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl restart nginx
```

### 5. SSL Certificate (Let's Encrypt)

```bash
sudo apt install certbot python3-certbot-nginx -y
sudo certbot --nginx -d panlasapp.food -d www.panlasapp.food
```

### 6. Start Application

```bash
pm2 start deployment/linode/ecosystem.config.js
pm2 save
pm2 startup
```

## File Structure

```
deployment/linode/
├── README.md                    # This file
├── deploy.sh                    # Automated deployment script
├── ecosystem.config.js          # PM2 configuration
├── nginx.conf                   # Nginx configuration
├── .env.production              # Frontend environment variables
├── .env.backend.production      # Backend environment variables
└── update.sh                    # Update script for future deployments
```

## Monitoring and Maintenance

### Check Application Status
```bash
pm2 status
pm2 logs
```

### Check Nginx Status
```bash
sudo systemctl status nginx
sudo nginx -t
```

### Update Application
```bash
cd /var/www/panlasapp
./deployment/linode/update.sh
```

## Troubleshooting

### Common Issues

1. **Port 3000 or 5000 already in use**
   ```bash
   sudo lsof -i :3000
   sudo lsof -i :5000
   sudo kill -9 <PID>
   ```

2. **Permission issues**
   ```bash
   sudo chown -R $USER:$USER /var/www/panlasapp
   ```

3. **MongoDB connection issues**
   ```bash
   sudo systemctl status mongod
   sudo systemctl restart mongod
   ```

4. **Nginx configuration errors**
   ```bash
   sudo nginx -t
   sudo systemctl status nginx
   ```

## Security Considerations

- Change default MongoDB authentication
- Configure firewall (ufw)
- Regular security updates
- Monitor application logs
- Backup database regularly

## Support

For issues specific to this deployment, check:
1. PM2 logs: `pm2 logs`
2. Nginx logs: `sudo tail -f /var/log/nginx/error.log`
3. MongoDB logs: `sudo tail -f /var/log/mongodb/mongod.log`
