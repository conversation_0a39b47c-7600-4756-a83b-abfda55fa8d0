#!/usr/bin/env node

/**
 * PanlasApp Deployment Setup Script
 * This script helps prepare the application for deployment
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 PanlasApp Deployment Setup');
console.log('==============================\n');

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ Error: package.json not found. Please run this script from the frontend root directory.');
  process.exit(1);
}

// Check if backend directory exists
const backendPath = path.join(process.cwd(), 'meal-planner-backend');
const backendExists = fs.existsSync(backendPath);

console.log('📋 Pre-deployment Checklist:');
console.log('============================\n');

// Frontend checks
console.log('Frontend (Vercel):');
console.log('✅ package.json found');
console.log('✅ vite.config.js configured');
console.log('✅ vercel.json created');

const envExampleExists = fs.existsSync('.env.example');
console.log(`${envExampleExists ? '✅' : '❌'} .env.example exists`);

const envLocalExists = fs.existsSync('.env.local');
console.log(`${envLocalExists ? '✅' : '⚠️'} .env.local ${envLocalExists ? 'exists' : 'needs to be created'}`);

// Backend checks
console.log('\nBackend (Railway):');
if (backendExists) {
  console.log('✅ Backend directory found');
  
  const backendPackageJson = path.join(backendPath, 'package.json');
  console.log(`${fs.existsSync(backendPackageJson) ? '✅' : '❌'} Backend package.json exists`);
  
  const railwayJson = path.join(backendPath, 'railway.json');
  console.log(`${fs.existsSync(railwayJson) ? '✅' : '❌'} railway.json exists`);
  
  const procfile = path.join(backendPath, 'Procfile');
  console.log(`${fs.existsSync(procfile) ? '✅' : '❌'} Procfile exists`);
  
  const backendEnvExample = path.join(backendPath, '.env.example');
  console.log(`${fs.existsSync(backendEnvExample) ? '✅' : '❌'} Backend .env.example exists`);
} else {
  console.log('❌ Backend directory not found');
}

console.log('\n📝 Next Steps:');
console.log('==============\n');

if (!envLocalExists) {
  console.log('1. Create .env.local file:');
  console.log('   cp .env.example .env.local');
  console.log('   Then edit .env.local with your backend URL\n');
}

console.log('2. Deploy Backend to Railway:');
console.log('   - Go to https://railway.app');
console.log('   - Connect your GitHub repository');
console.log('   - Select the meal-planner-backend directory');
console.log('   - Configure environment variables from .env.example');
console.log('   - Note the Railway URL for frontend configuration\n');

console.log('3. Deploy Frontend to Vercel:');
console.log('   - Go to https://vercel.com');
console.log('   - Connect your GitHub repository');
console.log('   - Set root directory to: PanlasApp Website/panlasapp web');
console.log('   - Add VITE_API_URL environment variable with Railway URL\n');

console.log('4. Update CORS settings:');
console.log('   - Add your Vercel domain to Railway environment variables');
console.log('   - Set FRONTEND_URL and CORS_ORIGIN in Railway\n');

console.log('📚 For detailed instructions, see DEPLOYMENT.md');

console.log('\n🎉 Setup complete! Ready for deployment.');
