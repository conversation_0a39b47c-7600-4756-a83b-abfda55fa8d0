# User Activity Logs Feature

## Overview
Added a comprehensive User Activity Logs section to the Admin Dashboard that tracks and displays all user activities across the website with filtering, pagination, and detailed information.

## Features Implemented

### 1. Admin Dashboard Integration
- **New Tab**: Added "User Activity Logs" tab to the admin dashboard
- **Consistent Styling**: Matches the existing admin dashboard theme and design
- **Hover Effects**: Same color scheme and hover animations as other sections
- **Responsive Design**: Mobile-friendly layout with proper responsive breakpoints

### 2. Activity Tracking Backend
- **Activity Model**: Enhanced existing Activity model with comprehensive action types
- **Activity Controller**: Updated with advanced filtering and pagination
- **Activity Routes**: Properly configured API endpoints for activity management

### 3. Tracked Activities
The system now tracks the following user activities:
- **Login**: User authentication events
- **Logout**: User session termination
- **Create Meal Plan**: New meal plan creation
- **Update Meal Plan**: Meal plan modifications
- **Delete Meal Plan**: Meal plan removal
- **Update Profile**: User profile changes
- **Create Meal**: New meal creation (ready for future implementation)

### 4. Activity Logs UI Features

#### Statistics Dashboard
- **Total Activities**: Shows overall activity count
- **Current Page Items**: Displays items on current page
- **Total Pages**: Shows pagination information
- **Visual Cards**: Color-coded statistics with hover effects

#### Advanced Filtering
- **Action Type Filter**: Filter by specific activity types
- **User Search**: Search by username or email
- **Date Range Filter**: Filter activities by date range (from/to)
- **Real-time Filtering**: Instant results with pagination reset

#### Activity Table
- **User Information**: Username and email display
- **Action Badges**: Color-coded action type indicators
- **Descriptions**: Human-readable activity descriptions
- **IP Address**: Security tracking with monospace font
- **Timestamps**: Formatted date and time display
- **Details Expansion**: Collapsible JSON details view

#### Pagination
- **Page Navigation**: Previous/Next buttons with state management
- **Page Information**: Current page and total pages display
- **Disabled States**: Proper button states for navigation limits

### 5. Color-Coded Action Types
- **Login**: Green background (success)
- **Logout**: Red background (termination)
- **Create Meal Plan**: Blue background (creation)
- **Update Meal Plan**: Yellow background (modification)
- **Delete Meal Plan**: Red background (deletion)
- **Update Profile**: Gray background (general)
- **Create Meal**: Green background (creation)

### 6. Responsive Design
- **Mobile Optimization**: Stacked table layout on mobile devices
- **Touch-Friendly**: Proper button sizes and spacing
- **Flexible Grid**: Auto-adjusting filter and statistics grids
- **Readable Text**: Appropriate font sizes for all screen sizes

## Technical Implementation

### Frontend Components
- **AdminDashboard.jsx**: Main dashboard with new activity logs tab
- **AdminDashboard.css**: Enhanced styles for activity logs section
- **State Management**: React hooks for filters, pagination, and data

### Backend Enhancements
- **activityController.js**: Enhanced with advanced filtering and pagination
- **userController.js**: Added activity logging for login and profile updates
- **mealPlanController.js**: Added activity logging for meal plan operations
- **Activity Model**: Comprehensive activity tracking schema

### API Endpoints
- `GET /api/activity/log`: Get paginated activity logs with filtering
- `POST /api/activity/log`: Log new user activity
- `GET /api/activity/user/:userId`: Get activities for specific user
- `GET /api/activity/recent`: Get recent activities

### Query Parameters
- `page`: Page number for pagination
- `limit`: Items per page (default: 50)
- `action`: Filter by action type
- `user`: Search by username or email
- `dateFrom`: Start date for filtering
- `dateTo`: End date for filtering

## Security Features
- **Admin Only Access**: Activity logs restricted to admin users
- **IP Address Tracking**: Security monitoring with IP logging
- **User Agent Logging**: Device and browser information
- **Timestamp Precision**: Accurate activity timing

## User Experience
- **Intuitive Interface**: Easy-to-use filters and navigation
- **Visual Feedback**: Loading states and hover effects
- **Information Density**: Comprehensive data without clutter
- **Performance**: Efficient pagination and filtering

## Future Enhancements
- **Export Functionality**: CSV/PDF export of activity logs
- **Real-time Updates**: WebSocket integration for live activity feed
- **Advanced Analytics**: Activity patterns and user behavior insights
- **Audit Trail**: Enhanced security logging and compliance features
- **Activity Alerts**: Notifications for suspicious activities

## Usage
1. Navigate to Admin Dashboard
2. Click on "User Activity Logs" tab
3. Use filters to narrow down activities
4. Browse through paginated results
5. Click on "View Details" to see additional information
6. Use pagination controls to navigate through pages

The feature provides administrators with comprehensive visibility into user activities while maintaining the consistent design language of the entire application.
