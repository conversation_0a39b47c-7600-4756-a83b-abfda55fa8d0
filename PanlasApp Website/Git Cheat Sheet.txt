RULES:
FOR CREATING feature branch
1. git pull origin main
2. Switch to "main" branch
3. git swicth -c "feature branch name"
4. git commit -am "Commit Message"
5. git push origin <feature branch name>

FOR MERGING branches
1. Go to GitHub repo
2. Click Pull Request Tab
3. Click New Pull Request(green button)
4. compare -> choose feature branch; 
   base -> choose main branch 
5. Click Create Pull Request(green button)
6. Click "Merge"  
note: If new changes create by other users -> review code -> merge code change

TL:DR
Feature BRANCH ->  main only

After all features are working NO BUGS!!
1. Go to GitHub repo
2. Click Pull Request Tab
3. Click New Pull Request(green button)
4. compare -> choose main branch; 
   base -> choose PROD branch 
5. Click Create Pull Request(green button)
6. Click "Merge"  

=================================================================================================================================================

How to pull or sync your repo from github to local(Your PC) > git pull origin <main branch of repo> 
Best Practice: Everytime you open your PC, always pull from github repo

=================================================================================================================================================

How to create a feature branch > git switch -c <Feature branch name>
Best Practice: Always create a feature branch from main branch if you want to test for avoiding any changes from main or default branch

=================================================================================================================================================

How to switch from existing branch > git switch <existing branch name> 
example: git switch main

=================================================================================================================================================

How to add new files/folders > git add -A
Note: -A is for newly created files added in repo

=================================================================================================================================================

How to commit changes in code > git commit -am "<comment for added features">
Note: -am means: 
"a" for add files(just to be sure 100% & this might not work if adding new files, this is why you use git add -A) 
"m" is for the comment message
Best practice: keep comment short and straight to the point before push changes to github repo

=================================================================================================================================================

How to push changes to github repo > git push origin <feature branch name> 
Best practice: never add new features from main 

=================================================================================================================================================

How to add other branchs not existing in my pc > git fetch --all
Note: fetch --all means all new branches created & commited in github repo will be sync to your PC

=================================================================================================================================================

How to verify which branch are you using > git branch 

=================================================================================================================================================

How to delete feature branch from local PC > git branch -D 

=================================================================================================================================================

How to check if your changes already commited and/or new files are added > git status

=================================================================================================================================================