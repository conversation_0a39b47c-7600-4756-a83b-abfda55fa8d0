const axios = require('axios');

async function testBackendConnection() {
  try {
    console.log('🔍 Testing backend connection...');
    
    // Test basic connection
    const response = await axios.get('http://localhost:5000/api/routes');
    console.log('✅ Backend is running!');
    console.log('Status:', response.status);
    console.log('Available routes:', response.data.length, 'routes found');
    
    return true;
  } catch (error) {
    console.error('❌ Backend connection failed:');
    if (error.code === 'ECONNREFUSED') {
      console.error('  - Backend server is not running on port 5000');
      console.error('  - Please start the backend server first');
    } else {
      console.error('  - Error:', error.message);
    }
    return false;
  }
}

testBackendConnection();
