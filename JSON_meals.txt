[{"name": "<PERSON><PERSON><PERSON>", "mealType": ["lunch", "dinner"], "category": "Meat", "dietaryTags": ["high-protein", "low-carb"], "rating": 4.8, "prepTime": 45, "calories": 320, "protein": 28, "carbs": 8, "fat": 18, "price": 250, "image": "/imagesfood/adobo_manok.jpg", "description": "A classic Filipino dish of chicken marinated and simmered in soy sauce, vinegar, and spices. This savory and slightly tangy dish is considered by many as the unofficial national dish of the Philippines.", "ingredients": ["2 lbs chicken pieces", "1/3 cup soy sauce", "1/3 cup vinegar", "6 cloves garlic, crushed", "1 tsp black peppercorns", "3 bay leaves", "2 tbsp cooking oil"], "instructions": ["Combine chicken, soy sauce, vinegar, garlic, peppercorns, and bay leaves in a large pot.", "Marinate for at least 30 minutes.", "Bring to a boil, then reduce heat and simmer for 30 minutes until chicken is tender.", "Remove chicken and set aside.", "Continue to simmer the sauce until it reduces by half.", "In a separate pan, heat oil and fry the chicken pieces until brown.", "Pour the reduced sauce over the chicken and serve hot with rice."], "allergens": ["soy"], "dietType": {"isVegetarian": false, "isVegan": false, "isGlutenFree": false, "isDairyFree": true, "isNutFree": true, "isLowCarb": true, "isKeto": true, "isPescatarian": false, "isHalal": true}}, {"name": "Sinigang na Baboy", "mealType": ["lunch", "dinner"], "category": "Soup", "dietaryTags": ["high-protein", "low-carb"], "rating": 4.7, "prepTime": 60, "calories": 380, "protein": 25, "carbs": 15, "fat": 22, "price": 320, "image": "/imagesfood/pork_sinigang.jpg", "description": "A sour soup made with pork, vegetables, and tamarind. This refreshing dish is perfect for rainy days and is known for its distinctive tangy flavor that Filipinos love.", "ingredients": ["2 lbs pork ribs or belly", "1 packet sinigang mix (or 2 cups fresh tamarind pulp)", "1 onion, quartered", "2 tomatoes, quartered", "2 eggplants, sliced", "1 bundle string beans, cut into 2-inch pieces", "1 bunch kangkong (water spinach) or spinach", "2 pieces radish, sliced", "3 pieces green chili", "Fish sauce to taste", "8 cups water"], "instructions": ["In a large pot, bring water to a boil.", "Add pork and simmer for 30 minutes until tender.", "Add onions, tomatoes, and radish. <PERSON> for 5 minutes.", "Add sinigang mix or tamarind pulp and stir until dissolved.", "Add eggplants and string beans. <PERSON> for 5 minutes.", "Add kangkong or spinach and green chili. Cook for 1 minute.", "Season with fish sauce to taste.", "Serve hot with rice."], "allergens": ["eggs", "fish"], "dietType": {"isVegetarian": false, "isVegan": false, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": true, "isKeto": false, "isPescatarian": false, "isHalal": false}}, {"name": "Pancit Canton", "mealType": ["lunch", "appetizer"], "category": "Noodles", "dietaryTags": ["high-protein", "contains-gluten"], "rating": 4.6, "prepTime": 30, "calories": 420, "protein": 18, "carbs": 65, "fat": 12, "image": "/imagesfood/filipino_pancit.jpg", "description": "A Filipino stir-fried noodle dish made with egg noodles, meat, and vegetables. Commonly served during birthdays and special occasions as noodles symbolize long life in Filipino culture.", "ingredients": ["1 lb pancit canton (egg noodles)", "1/2 lb chicken breast, sliced", "1/4 lb pork, sliced", "1 cup cabbage, shredded", "1 cup carrots, julienned", "1 cup snap peas", "1/4 cup soy sauce", "2 tbsp oyster sauce", "4 cloves garlic, minced", "1 onion, sliced", "3 tbsp cooking oil", "1 cup chicken broth", "Calamansi or lemon wedges for serving"], "instructions": ["Soak noodles in warm water for 10 minutes, then drain.", "Heat oil in a wok. Sauté garlic and onions until fragrant.", "Add chicken and pork, cook until browned.", "Add vegetables and stir-fry for 2 minutes.", "Add soy sauce, oyster sauce, and chicken broth. Bring to a simmer.", "Add noodles and toss until well coated and liquid is absorbed.", "Serve hot with calamansi or lemon wedges."], "price": 180, "allergens": ["gluten", "soy", "eggs", "shellfish"], "dietType": {"isVegetarian": false, "isVegan": false, "isGlutenFree": false, "isDairyFree": true, "isNutFree": true, "isLowCarb": false, "isKeto": false, "isPescatarian": false, "isHalal": false}}, {"name": "Chicken Arroz <PERSON>", "mealType": ["breakfast"], "category": "Soup", "dietaryTags": ["high-protein"], "rating": 4.5, "prepTime": 45, "calories": 350, "protein": 22, "carbs": 45, "fat": 10, "image": "/imagesfood/lugaw.jpg", "description": "A Filipino rice porridge with chicken, ginger, and toppings like boiled egg and green onions. This comforting dish is perfect for breakfast or when feeling under the weather.", "ingredients": ["1 cup glutinous rice (or regular rice)", "1 lb chicken pieces", "2 tbsp fish sauce", "1 tbsp vegetable oil", "1 onion, diced", "4 cloves garlic, minced", "2-inch piece ginger, julienned", "6 cups chicken broth", "Hard-boiled eggs, sliced (for topping)", "Green onions, chopped (for topping)", "Fried garlic (for topping)", "Calamansi or lemon wedges (for serving)"], "instructions": ["Heat oil in a pot. Sauté garlic, onion, and ginger until fragrant.", "Add chicken pieces and cook until lightly browned.", "Add fish sauce and stir for 1 minute.", "Add rice and stir to coat with oil.", "Pour in chicken broth and bring to a boil.", "Reduce heat and simmer for 30-35 minutes, stirring occasionally, until rice is very soft and porridge is thick.", "Serve hot topped with sliced eggs, green onions, and fried garlic. Serve with calamansi or lemon wedges on the side."], "price": 230, "allergens": ["eggs", "fish"], "dietType": {"isVegetarian": false, "isVegan": false, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": false, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "<PERSON><PERSON><PERSON>", "mealType": ["lunch", "dinner"], "category": "Meat", "dietaryTags": ["high-protein"], "rating": 4.9, "prepTime": 90, "calories": 650, "protein": 35, "carbs": 5, "fat": 55, "image": "/imagesfood/lechon_kawali.jpg", "description": "Crispy deep-fried pork belly that's first boiled with spices then air-dried before frying. Known for its crunchy skin and tender meat, it's often served with a dipping sauce made of vinegar, soy sauce, and chili.", "ingredients": ["2 lbs pork belly, whole piece", "2 tbsp salt", "1 tsp black peppercorns", "5 cloves garlic, crushed", "3 bay leaves", "1 onion, quartered", "Water for boiling", "Oil for deep frying", "Lechon sauce or liver sauce for serving"], "instructions": ["In a large pot, place pork belly, salt, peppercorns, garlic, bay leaves, and onion. Add water to cover.", "Bring to a boil, then reduce heat and simmer for 45-60 minutes until tender but not falling apart.", "Remove pork and let it cool completely. Pat dry and refrigerate uncovered for at least 4 hours or overnight.", "Heat oil in a deep pan or wok for deep frying.", "Carefully add the pork belly, skin side down first. Fry until golden and crispy, turning to cook all sides.", "Remove and drain on paper towels. Let rest for 5 minutes.", "Chop into serving pieces and serve with lechon sauce or a dipping sauce of vinegar, soy sauce, and chili."], "price": 430, "allergens": [], "dietType": {"isVegetarian": false, "isVegan": false, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": true, "isKeto": true, "isPescatarian": false, "isHalal": false}}, {"name": "<PERSON><PERSON>", "mealType": ["lunch", "breakfast"], "category": "Meat", "dietaryTags": ["high-protein"], "rating": 4.8, "prepTime": 25, "calories": 580, "protein": 30, "carbs": 60, "fat": 22, "image": "/imagesfood/beef_tapa.jpg", "description": "Thinly sliced beef marinated in soy sauce, garlic, and spices, then fried until slightly crispy. It's typically served as part of a 'tapsilog' breakfast meal with garlic rice and fried egg.", "ingredients": ["1 lb beef sirloin, thinly sliced", "1/4 cup soy sauce", "2 tbsp brown sugar", "1 head garlic, minced", "2 tbsp vinegar", "1 tsp ground black pepper", "2 tbsp cooking oil", "Garlic fried rice and fried egg for serving", "Sliced tomatoes and cucumber for garnish"], "instructions": ["In a bowl, combine soy sauce, brown sugar, garlic, vinegar, and black pepper.", "Add beef slices and marinate for at least 4 hours or overnight in the refrigerator.", "Heat oil in a pan over medium-high heat.", "Fry marinated beef slices until browned and slightly crispy, about 3-5 minutes.", "Serve hot with garlic fried rice, fried egg, and sliced tomatoes and cucumber on the side."], "price": 490, "allergens": ["soy", "eggs"], "dietType": {"isVegetarian": false, "isVegan": false, "isGlutenFree": false, "isDairyFree": true, "isNutFree": true, "isLowCarb": false, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mealType": ["lunch", "dinner"], "category": "<PERSON><PERSON>", "dietaryTags": ["high-protein", "contains-nuts"], "rating": 4.7, "prepTime": 120, "calories": 520, "protein": 28, "carbs": 25, "fat": 35, "image": "/imagesfood/kare_kare.jpg", "description": "A rich Filipino stew made with oxtail, tripe, and vegetables in a thick peanut sauce. This festive dish is traditionally served during special occasions and is best paired with bagoong (shrimp paste).", "ingredients": ["2 lbs oxtail, cut into 2-inch pieces", "1/2 lb tripe (optional), cleaned and sliced", "1 bundle string beans, cut into 2-inch pieces", "2 eggplants, sliced", "1 banana blossom (optional), sliced", "1/2 cup ground peanuts", "1/4 cup peanut butter", "2 tbsp annatto powder or annatto oil", "2 tbsp rice flour (dissolved in water)", "1 onion, diced", "4 cloves garlic, minced", "Salt and pepper to taste", "Bagoong (shrimp paste) for serving"], "instructions": ["In a large pot, boil oxtail and tripe in water with salt for 1-2 hours until tender. Reserve the broth.", "In another pot, heat oil and sauté garlic and onions until fragrant.", "Add cooked oxtail and tripe, ground peanuts, peanut butter, and annatto powder. Stir well.", "Add 4 cups of the reserved broth and bring to a simmer.", "Add vegetables and cook until tender but still crisp.", "Thicken the sauce with dissolved rice flour.", "Season with salt and pepper to taste.", "Serve hot with bagoong (shrimp paste) on the side."], "price": 580, "allergens": ["peanuts"], "dietType": {"isVegetarian": false, "isVegan": false, "isGlutenFree": false, "isDairyFree": true, "isNutFree": false, "isLowCarb": false, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "Pinakbet", "mealType": ["lunch", "dinner"], "category": "Vegetable", "dietaryTags": ["vegan", "gluten-free"], "rating": 4.5, "prepTime": 40, "calories": 220, "protein": 10, "carbs": 25, "fat": 12, "image": "/imagesfood/pinakbet.jpg", "description": "A traditional Filipino vegetable dish made with mixed vegetables sautéed in fish or shrimp paste. This Ilocano specialty showcases the bounty of Filipino produce.", "ingredients": ["1/4 lb pork belly, sliced (optional)", "1 bitter melon (ampalaya), seeded and sliced", "1 eggplant, sliced", "1 bundle string beans, cut into 2-inch pieces", "1 squash, cubed", "2 tomatoes, quartered", "1 onion, sliced", "4 cloves garlic, minced", "2 tbsp shrimp paste (bagoong)", "1/2 cup water", "2 tbsp cooking oil", "Salt and pepper to taste"], "instructions": ["Heat oil in a pot. If using pork, sauté until browned.", "Add garlic and onions. Sauté until fragrant.", "Add tomatoes and cook until softened.", "Add shrimp paste and stir for 1 minute.", "Add water and bring to a simmer.", "Add squash and cook for 5 minutes.", "Add eggplant, string beans, and bitter melon. Cook for another 5-7 minutes until vegetables are tender but still firm.", "Season with salt and pepper to taste.", "Serve hot with rice."], "price": 200, "allergens": [], "dietType": {"isVegetarian": false, "isVegan": false, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": false, "isKeto": false, "isPescatarian": false, "isHalal": false}}, {"name": "Pinakbet", "mealType": ["lunch", "dinner"], "category": "Vegetable", "dietaryTags": ["vegan", "gluten-free"], "rating": 4.4, "prepTime": 25, "calories": 200, "protein": 8, "carbs": 30, "fat": 8, "image": "/imagesfood/pinakbet.jpg", "description": "A traditional Filipino vegetable stew made with mixed vegetables and bagoong (shrimp paste). This version uses vegan alternatives for a plant-based option.", "ingredients": ["1 cup kalabasa (squash), cubed", "1 cup sitaw (string beans), cut into 2-inch pieces", "1 cup okra, sliced", "1 cup eggplant, sliced", "1 cup ampalaya (bitter gourd), sliced", "2 tomatoes, sliced", "1 onion, sliced", "3 cloves garlic, minced", "2 tbsp vegan bagoong or soy sauce", "2 tbsp cooking oil", "1/2 cup water"], "instructions": ["Heat oil in a pan and sauté garlic, onion, and tomatoes until soft.", "Add the harder vegetables first (squash, string beans) and cook for 5 minutes.", "Add remaining vegetables and vegan bagoong or soy sauce.", "Add water and simmer until vegetables are tender but still crisp.", "Serve hot with rice."], "price": 100, "allergens": ["soy", "fish"], "dietType": {"isVegetarian": true, "isVegan": true, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": false, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "mealType": ["lunch", "appetizer"], "category": "Vegetable", "dietaryTags": ["vegan"], "rating": 4.3, "prepTime": 45, "calories": 180, "protein": 6, "carbs": 25, "fat": 8, "image": "/imagesfood/lumpiang_gulay.jpg", "description": "Fresh vegetable spring rolls wrapped in thin crepe-like wrapper, filled with mixed vegetables and served with a sweet and savory sauce.", "ingredients": ["20 pieces lumpia wrapper", "2 cups cabbage, shredded", "1 cup carrots, julienned", "1 cup bean sprouts", "1 cup lettuce leaves", "1/2 cup tofu, cubed and fried", "3 cloves garlic, minced", "1 onion, sliced", "2 tbsp soy sauce", "1 tbsp cooking oil", "Sweet chili sauce for serving"], "instructions": ["Heat oil and sauté garlic and onion until fragrant.", "Add vegetables and tofu, stir-fry for 3 minutes.", "Season with soy sauce and let cool.", "Place filling in lumpia wrapper and roll tightly.", "Serve fresh with sweet chili sauce."], "price": 150, "allergens": ["gluten", "soy"], "dietType": {"isVegetarian": true, "isVegan": true, "isGlutenFree": false, "isDairyFree": true, "isNutFree": true, "isLowCarb": false, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "Gising-Gising", "mealType": ["lunch", "dinner"], "category": "Vegetable", "dietaryTags": ["vegan", "gluten-free", "spicy"], "rating": 4.4, "prepTime": 25, "calories": 180, "protein": 7, "carbs": 14, "fat": 10, "image": "/imagesfood/gising_gising.jpg", "description": "A spicy Filipino vegetable dish made with chopped green beans and coconut milk, flavored with chili and garlic.", "ingredients": ["2 cups green beans, chopped", "1 cup coconut milk", "1 onion, minced", "4 cloves garlic, minced", "2-3 Thai chili peppers, chopped", "1 tbsp cooking oil", "Salt and pepper to taste"], "instructions": ["Heat oil in a pan and sauté garlic and onion until fragrant.", "Add green beans and cook for 2 minutes.", "Pour in coconut milk and add chili peppers.", "Simmer until beans are tender and sauce thickens.", "Season with salt and pepper. Serve hot."], "price": 90, "allergens": [], "dietType": {"isVegetarian": true, "isVegan": true, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": true, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "Ensaladang Talong", "mealType": ["lunch", "dinner", "appetizer"], "category": "Salad", "dietaryTags": ["vegan", "gluten-free"], "rating": 4.3, "prepTime": 20, "calories": 90, "protein": 2, "carbs": 10, "fat": 5, "image": "/imagesfood/ensaladang_talong.jpg", "description": "A smoky eggplant salad with tomatoes and onions, dressed with vinegar. A refreshing Filipino side dish.", "ingredients": ["2 eggplants", "2 tomatoes, diced", "1 onion, diced", "1/4 cup vinegar", "Salt and pepper to taste"], "instructions": ["Grill or roast eggplants until skin is charred. Peel and chop.", "Combine eggplant, tomatoes, and onion in a bowl.", "Add vinegar, salt, and pepper. Toss and serve."], "price": 60, "allergens": [], "dietType": {"isVegetarian": true, "isVegan": true, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": true, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "<PERSON><PERSON>", "mealType": ["lunch", "dinner"], "category": "Soup", "dietaryTags": ["vegan", "gluten-free"], "rating": 4.5, "prepTime": 40, "calories": 210, "protein": 12, "carbs": 30, "fat": 4, "image": "/imagesfood/mongo_guisado.jpg", "description": "A hearty mung bean stew with leafy greens and tomatoes. A Filipino comfort food staple.", "ingredients": ["1 cup mung beans", "6 cups water", "2 cups spinach or malunggay leaves", "2 tomatoes, diced", "1 onion, chopped", "4 cloves garlic, minced", "1 tbsp cooking oil", "Salt and pepper to taste"], "instructions": ["Boil mung beans in water until soft.", "In another pan, sauté garlic, onion, and tomatoes.", "Add cooked mung beans and simmer.", "Add greens and cook until wilted.", "Season with salt and pepper. Serve hot."], "price": 70, "allergens": [], "dietType": {"isVegetarian": true, "isVegan": true, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": false, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "<PERSON><PERSON><PERSON>", "mealType": ["breakfast", "lunch"], "category": "Vegetable", "dietaryTags": ["vegetarian", "gluten-free"], "rating": 4.4, "prepTime": 25, "calories": 180, "protein": 7, "carbs": 10, "fat": 12, "image": "/imagesfood/tortang_talong.jpg", "description": "A Filipino eggplant omelette made by grilling eggplant and dipping it in beaten eggs before frying.", "ingredients": ["2 eggplants", "2 eggs", "Salt and pepper to taste", "2 tbsp cooking oil"], "instructions": ["Grill eggplants until skin is charred. Peel and flatten.", "Beat eggs with salt and pepper.", "Dip eggplants in egg mixture.", "Fry in oil until golden brown on both sides.", "Serve hot."], "price": 70, "allergens": ["eggs"], "dietType": {"isVegetarian": true, "isVegan": false, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": true, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "Gina<PERSON><PERSON>", "mealType": ["lunch", "dinner"], "category": "Vegetable", "dietaryTags": ["vegan", "gluten-free"], "rating": 4.3, "prepTime": 35, "calories": 160, "protein": 5, "carbs": 18, "fat": 8, "image": "/imagesfood/ginataang_langka.jpg", "description": "Unripe jackfruit cooked in coconut milk with spices. A creamy, savory Filipino vegan dish.", "ingredients": ["2 cups unripe jackfruit, shredded", "1 cup coconut milk", "1 onion, sliced", "4 cloves garlic, minced", "1 tbsp ginger, minced", "2 tbsp cooking oil", "Salt and pepper to taste"], "instructions": ["Heat oil in a pan and sauté garlic, onion, and ginger.", "Add jackfruit and cook for 2 minutes.", "Pour in coconut milk and simmer until jackfruit is tender.", "Season with salt and pepper. Serve hot."], "price": 80, "allergens": [], "dietType": {"isVegetarian": true, "isVegan": true, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": false, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "Ukoy", "mealType": ["snack", "appetizer"], "category": "Vegetable", "dietaryTags": ["vegetarian"], "rating": 4.2, "prepTime": 30, "calories": 220, "protein": 6, "carbs": 30, "fat": 10, "image": "/imagesfood/ukoy.jpg", "description": "Crispy Filipino vegetable fritters made with mung bean sprouts, carrots, and sweet potatoes.", "ingredients": ["1 cup mung bean sprouts", "1 cup grated sweet potato", "1/2 cup carrots, julienned", "1/2 cup all-purpose flour", "1/4 cup cornstarch", "1 egg", "1/2 cup water", "Salt and pepper to taste", "Oil for frying"], "instructions": ["Mix all vegetables in a bowl.", "Add flour, cornstarch, egg, water, salt, and pepper. Mix to form a batter.", "Spoon mixture into hot oil and fry until golden brown.", "Drain and serve with vinegar dipping sauce."], "price": 60, "allergens": ["eggs", "gluten"], "dietType": {"isVegetarian": true, "isVegan": false, "isGlutenFree": false, "isDairyFree": true, "isNutFree": true, "isLowCarb": false, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "Paksiw na Bangus", "mealType": ["lunch", "dinner"], "category": "Fish", "dietaryTags": ["pescatarian", "gluten-free", "low-carb"], "rating": 4.6, "prepTime": 35, "calories": 210, "protein": 22, "carbs": 4, "fat": 12, "image": "/imagesfood/paksiw_bangus.jpg", "description": "Milkfish stewed in vinegar, garlic, and vegetables. A tangy, healthy Filipino fish dish.", "ingredients": ["1 whole bangus (milkfish), cleaned and sliced", "1/2 cup vinegar", "1 cup water", "4 cloves garlic, crushed", "1 onion, sliced", "1 thumb ginger, sliced", "2 eggplants, sliced", "2 green chili peppers", "Salt and pepper to taste"], "instructions": ["Arrange fish and vegetables in a pot.", "Add vinegar, water, garlic, onion, ginger, and chili.", "Season with salt and pepper.", "Simmer until fish is cooked and vegetables are tender.", "Serve hot."], "price": 160, "allergens": ["fish"], "dietType": {"isVegetarian": false, "isVegan": false, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": true, "isKeto": true, "isPescatarian": true, "isHalal": true}}, {"name": "<PERSON><PERSON> na Gabi", "mealType": ["lunch", "dinner"], "category": "Vegetable", "dietaryTags": ["vegan", "gluten-free", "spicy"], "rating": 4.5, "prepTime": 50, "calories": 200, "protein": 5, "carbs": 14, "fat": 14, "image": "/imagesfood/laing_gabi.jpg", "description": "Taro leaves cooked in coconut milk with chili and ginger. A spicy, creamy Bicolano vegan dish.", "ingredients": ["2 cups dried taro leaves", "2 cups coconut milk", "1 cup coconut cream", "1 onion, sliced", "4 cloves garlic, minced", "1 thumb ginger, minced", "3-5 chili peppers, chopped", "Salt to taste"], "instructions": ["Sauté garlic, onion, and ginger.", "Add taro leaves and coconut milk. Simmer until leaves are soft.", "Add coconut cream and chili. Cook until thick.", "Season with salt. Serve hot."], "price": 100, "allergens": [], "dietType": {"isVegetarian": true, "isVegan": true, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": true, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "Pritong Tilapia", "mealType": ["lunch", "dinner"], "category": "Fish", "dietaryTags": ["pescatarian", "gluten-free", "low-carb"], "rating": 4.3, "prepTime": 20, "calories": 220, "protein": 24, "carbs": 2, "fat": 12, "image": "/imagesfood/pritong_tilapia.jpg", "description": "Crispy fried tilapia, a simple and popular Filipino fish dish.", "ingredients": ["1 whole tilapia, cleaned", "Salt and pepper to taste", "Oil for frying"], "instructions": ["Season tilapia with salt and pepper.", "Heat oil in a pan and fry tilapia until golden and crispy.", "Drain and serve hot."], "price": 120, "allergens": ["fish"], "dietType": {"isVegetarian": false, "isVegan": false, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": true, "isKeto": true, "isPescatarian": true, "isHalal": true}}, {"name": "Lumpiang Sariwa", "mealType": ["lunch", "appetizer"], "category": "Vegetable", "dietaryTags": ["vegetarian"], "rating": 4.5, "prepTime": 40, "calories": 170, "protein": 5, "carbs": 28, "fat": 5, "image": "/imagesfood/lumpiang_sariwa.jpg", "description": "Fresh Filipino spring rolls filled with sautéed vegetables, wrapped in a soft crepe, and served with sweet garlic sauce.", "ingredients": ["1 cup cabbage, shredded", "1 cup carrots, julienned", "1 cup green beans, sliced", "1/2 cup sweet potato, julienned", "1/2 cup tofu, cubed and fried", "2 cloves garlic, minced", "1 onion, sliced", "2 tbsp soy sauce", "1 tbsp cooking oil", "Lumpia wrapper or crepe"], "instructions": ["Sauté garlic and onion in oil.", "Add vegetables and tofu, stir-fry until tender.", "Season with soy sauce.", "Place filling in wrapper, roll, and serve with sweet garlic sauce."], "price": 80, "allergens": ["soy", "gluten"], "dietType": {"isVegetarian": true, "isVegan": false, "isGlutenFree": false, "isDairyFree": true, "isNutFree": true, "isLowCarb": false, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "<PERSON><PERSON>in na Labanos", "mealType": ["appetizer", "lunch"], "category": "Vegetable", "dietaryTags": ["vegan", "gluten-free"], "rating": 4.1, "prepTime": 15, "calories": 60, "protein": 2, "carbs": 8, "fat": 2, "image": "/imagesfood/kilawin_labanos.jpg", "description": "Radish salad marinated in vinegar, onions, and chili. A tangy, refreshing Filipino side.", "ingredients": ["2 cups radish, julienned", "1 onion, sliced", "1/4 cup vinegar", "1 chili pepper, sliced", "Salt and pepper to taste"], "instructions": ["Combine all ingredients in a bowl.", "Toss well and let sit for 10 minutes.", "Serve chilled."], "price": 40, "allergens": [], "dietType": {"isVegetarian": true, "isVegan": true, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": true, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "<PERSON><PERSON><PERSON> at Kalabasa", "mealType": ["lunch", "dinner"], "category": "Vegetable", "dietaryTags": ["vegan", "gluten-free"], "rating": 4.6, "prepTime": 35, "calories": 170, "protein": 5, "carbs": 18, "fat": 9, "image": "/imagesfood/ginataang_sitaw_kalabasa.jpg", "description": "String beans and squash cooked in creamy coconut milk, a classic Filipino vegan dish.", "ingredients": ["2 cups squash, cubed", "2 cups string beans, cut into 2-inch pieces", "1 cup coconut milk", "1 onion, sliced", "3 cloves garlic, minced", "1 tbsp ginger, minced", "2 tbsp cooking oil", "Salt and pepper to taste"], "instructions": ["Heat oil and sauté garlic, onion, and ginger.", "Add squash and cook for 5 minutes.", "Add string beans and coconut milk.", "Simmer until vegetables are tender.", "Season with salt and pepper. Serve hot."], "price": 90, "allergens": [], "dietType": {"isVegetarian": true, "isVegan": true, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": false, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "<PERSON><PERSON>", "mealType": ["lunch", "appetizer"], "category": "Salad", "dietaryTags": ["vegan", "gluten-free"], "rating": 4.2, "prepTime": 15, "calories": 70, "protein": 3, "carbs": 7, "fat": 3, "image": "/imagesfood/pako_salad.jpg", "description": "A refreshing salad made from fiddlehead ferns, tomatoes, and onions, tossed in a light vinaigrette.", "ingredients": ["2 cups pako (fiddlehead fern), cleaned", "2 tomatoes, sliced", "1 onion, sliced", "1/4 cup vinegar", "Salt and pepper to taste"], "instructions": ["Blanch pako in boiling water for 30 seconds, then drain.", "Combine with tomatoes and onions.", "Add vinegar, salt, and pepper. Toss and serve."], "price": 60, "allergens": [], "dietType": {"isVegetarian": true, "isVegan": true, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": true, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "<PERSON><PERSON><PERSON>", "mealType": ["lunch", "dinner"], "category": "Vegetable", "dietaryTags": ["vegan", "gluten-free", "low-carb"], "rating": 4.3, "prepTime": 20, "calories": 90, "protein": 4, "carbs": 10, "fat": 4, "image": "/imagesfood/adobong_sitaw.jpg", "description": "String beans cooked adobo-style with soy sauce, vinegar, and garlic.", "ingredients": ["2 cups sitaw (string beans), cut into 2-inch pieces", "1/4 cup soy sauce", "2 tbsp vinegar", "4 cloves garlic, minced", "1 onion, sliced", "1 tbsp cooking oil", "Salt and pepper to taste"], "instructions": ["Sauté garlic and onion in oil.", "Add string beans and cook for 2 minutes.", "Add soy sauce and vinegar.", "Simmer until beans are tender.", "Season with salt and pepper. Serve hot."], "price": 60, "allergens": ["soy"], "dietType": {"isVegetarian": true, "isVegan": true, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": true, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "<PERSON><PERSON><PERSON>", "mealType": ["appetizer", "lunch"], "category": "Salad", "dietaryTags": ["vegan", "gluten-free", "low-carb"], "rating": 4.1, "prepTime": 10, "calories": 40, "protein": 1, "carbs": 6, "fat": 1, "image": "/imagesfood/pipino_salad.jpg", "description": "A light cucumber salad with vinegar, onions, and tomatoes.", "ingredients": ["2 cups cucumber, sliced", "1 tomato, diced", "1 onion, sliced", "2 tbsp vinegar", "Salt and pepper to taste"], "instructions": ["Combine all ingredients in a bowl.", "Toss well and serve chilled."], "price": 30, "allergens": [], "dietType": {"isVegetarian": true, "isVegan": true, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": true, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "Ginataang Puso ng Saging", "mealType": ["lunch", "dinner"], "category": "Vegetable", "dietaryTags": ["vegan", "gluten-free"], "rating": 4.3, "prepTime": 35, "calories": 120, "protein": 4, "carbs": 14, "fat": 6, "image": "/imagesfood/ginataang_puso_ng_saging.jpg", "description": "Banana blossom cooked in coconut milk with garlic and onions.", "ingredients": ["2 cups banana blossom, sliced", "1 cup coconut milk", "1 onion, sliced", "3 cloves garlic, minced", "1 tbsp cooking oil", "Salt and pepper to taste"], "instructions": ["Sauté garlic and onion in oil.", "Add banana blossom and cook for 2 minutes.", "Add coconut milk and simmer until tender.", "Season with salt and pepper. Serve hot."], "price": 50, "allergens": [], "dietType": {"isVegetarian": true, "isVegan": true, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": false, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "Pritong Tokwa", "mealType": ["lunch", "appetizer"], "category": "Tofu", "dietaryTags": ["vegan", "gluten-free", "low-carb"], "rating": 4.2, "prepTime": 15, "calories": 110, "protein": 8, "carbs": 3, "fat": 7, "image": "/imagesfood/pritong_tokwa.jpg", "description": "Crispy fried tofu served with a soy-vinegar dipping sauce.", "ingredients": ["1 block firm tofu, cubed", "Oil for frying", "Salt to taste"], "instructions": ["Pat tofu dry and cut into cubes.", "Heat oil and fry tofu until golden and crispy.", "Drain and sprinkle with salt. Serve hot."], "price": 40, "allergens": ["soy"], "dietType": {"isVegetarian": true, "isVegan": true, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": true, "isKeto": true, "isPescatarian": false, "isHalal": true}}, {"name": "Inihaw na Talong", "mealType": ["lunch", "dinner"], "category": "Vegetable", "dietaryTags": ["vegan", "gluten-free", "low-carb"], "rating": 4.1, "prepTime": 20, "calories": 60, "protein": 2, "carbs": 8, "fat": 2, "image": "/imagesfood/inihaw_na_talong.jpg", "description": "Grilled eggplant served with vinegar and garlic dip.", "ingredients": ["2 eggplants", "Salt to taste"], "instructions": ["Grill eggplants until skin is charred.", "Peel, sprinkle with salt, and serve with vinegar dip."], "price": 30, "allergens": [], "dietType": {"isVegetarian": true, "isVegan": true, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": true, "isKeto": false, "isPescatarian": false, "isHalal": true}}, {"name": "Pangat na Isda", "mealType": ["lunch", "dinner"], "category": "Fish", "dietaryTags": ["pescatarian", "gluten-free", "low-carb"], "rating": 4.2, "prepTime": 30, "calories": 140, "protein": 18, "carbs": 2, "fat": 7, "image": "/imagesfood/pangat_na_isda.jpg", "description": "Fish simmered in tomatoes and sour broth, a light and healthy Filipino dish.", "ingredients": ["2 fish fillets (tilapia or bangus)", "2 tomatoes, sliced", "1 onion, sliced", "1 cup water", "Salt and pepper to taste"], "instructions": ["Arrange fish, tomatoes, and onion in a pot.", "Add water, salt, and pepper.", "Simmer until fish is cooked.", "Serve hot."], "price": 90, "allergens": ["fish"], "dietType": {"isVegetarian": false, "isVegan": false, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": true, "isKeto": true, "isPescatarian": true, "isHalal": true}}, {"name": "<PERSON><PERSON><PERSON> at Mais", "mealType": ["lunch", "dinner"], "category": "Vegetable", "dietaryTags": ["vegan", "gluten-free"], "rating": 4.0, "prepTime": 30, "calories": 130, "protein": 3, "carbs": 18, "fat": 6, "image": "/imagesfood/ginataang_kalabasa_mais.jpg", "description": "Squash and corn cooked in coconut milk, a sweet and creamy Filipino vegetable dish.", "ingredients": ["2 cups squash, cubed", "1 cup corn kernels", "1 cup coconut milk", "1 onion, sliced", "2 cloves garlic, minced", "1 tbsp cooking oil", "Salt and pepper to taste"], "instructions": ["Sauté garlic and onion in oil.", "Add squash and cook for 5 minutes.", "Add corn and coconut milk.", "Simmer until vegetables are tender.", "Season with salt and pepper. Serve hot."], "price": 50, "allergens": [], "dietType": {"isVegetarian": true, "isVegan": true, "isGlutenFree": true, "isDairyFree": true, "isNutFree": true, "isLowCarb": false, "isKeto": false, "isPescatarian": false, "isHalal": true}}]